package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhWasmLogModel = (*customVhWasmLogModel)(nil)

type (
	// VhWasmLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhWasmLogModel.
	VhWasmLogModel interface {
		vhWasmLogModel
	}

	customVhWasmLogModel struct {
		*defaultVhWasmLogModel
	}
)

// NewVhWasmLogModel returns a model for the database table.
func NewVhWasmLogModel(conn sqlx.SqlConn) VhWasmLogModel {
	return &customVhWasmLogModel{
		defaultVhWasmLogModel: newVhWasmLogModel(conn),
	}
}
