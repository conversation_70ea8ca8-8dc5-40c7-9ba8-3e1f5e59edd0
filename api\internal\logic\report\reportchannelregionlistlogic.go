package report

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportChannelRegionListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportChannelRegionListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportChannelRegionListLogic {
	return &ReportChannelRegionListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportChannelRegionListLogic) ReportChannelRegionList() (resp *types.ReportChannelRegionListResp, err error) {
	builder := squirrel.Select("DISTINCT(region_id),channel").From(l.svcCtx.ReportModel.GetTable())
	var result []*model.VhReportChannelRegion
	if err := l.svcCtx.ReportModel.FindRows(l.ctx, builder, &result); err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.ReportChannelRegionListResp)
	if len(result) > 0 {
		regionForChannel := make(map[int64][]*types.ReportRegionInfo)
		for _, region := range result {
			regionForChannel[region.Channel] = append(regionForChannel[region.Channel], &types.ReportRegionInfo{RegionsId: region.RegionId})
		}
		for channelId, regions := range regionForChannel {
			channelName := ""
			if name, ok := model.ChannelName[channelId]; ok { //防止以后添加后没设置
				channelName = name
			}
			resp.Channels = append(resp.Channels, &types.ReportChannelRegionInfo{
				ChannelId:   channelId,
				ChannelName: channelName,
				Regions:     regions,
			})
		}
	} else {
		resp.Channels = []interface{}{}
	}

	return resp, nil
}
