package buriedPointConfig

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type BuriedPointConfigListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBuriedPointConfigListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BuriedPointConfigListLogic {
	return &BuriedPointConfigListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BuriedPointConfigListLogic) BuriedPointConfigList(req *types.BuriedPointConfigListReq) (resp *types.BuriedPointConfigListResp, err error) {
	res := types.BuriedPointConfigListResp{
		Total: 0,
		List:  make([]types.BuriedPointConfigListItem, 0),
	}
	resp = &res

	// 获取总数
	builder := squirrel.Select("count(1)").
		From(l.svcCtx.BuriedPointConfigModel.GetTable())
	count, xerr := l.svcCtx.BuriedPointConfigModel.FindCount(context.Background(), builder)
	if xerr != nil {
		return
	}
	res.Total = count

	// 获取列表
	builder = squirrel.Select("*").From(l.svcCtx.BuriedPointConfigModel.GetTable())
	result := make([]model.VhBuriedPointConfig, 0)
	xerr = l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &result)
	if xerr != nil {
		return
	}

	// 类型列表
	var list []VinehooButton
	builder = squirrel.Select("id,name as title").From("vh_buried_point_genre")
	l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)

	// 频道列表
	var channel_list []VinehooButton
	builder = squirrel.Select("id,name as title").From("vh_buried_point_channel")
	l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &channel_list)

	for _, v := range result {
		// 类型名称
		var (
			senre_name   string
			channel_name string
		)
		for _, v1 := range list {
			if v1.Id == v.Genre {
				senre_name = v1.Title
			}
		}
		for _, v2 := range channel_list {
			if v2.Id == v.Channel {
				channel_name = v2.Title
			}
		}

		resp.List = append(resp.List, types.BuriedPointConfigListItem{
			Id:          v.Id,
			Genre:       v.Genre,
			GenreName:   senre_name,
			Channel:     v.Channel,
			ChannelName: channel_name,
			RegionId:    v.RegionId,
			RegionName:  v.RegionName,
			CreatedTime: time.Unix(v.CreatedTime, 0).Format("2006-01-02 15:04:05"),
		})
	}

	return
}
