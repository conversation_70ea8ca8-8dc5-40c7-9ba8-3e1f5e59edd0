package config

import (
	"engine/common/logger"
	"engine/common/tencentAdvert"
	"fmt"
	"github.com/vber/nacos/v2"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
)

type ApiConfig struct {
	rest.RestConf
	Es struct {
		Host     string
		Username string
		Password string
	}
	Mysql struct {
		DataSource string
	}
	MarketingMysql struct {
		DataSource string
	}
	Redis          redis.RedisConf
	TencentAd      tencentAdvert.Config
	Tencent264Ad   tencentAdvert.Config
	Tencent389Ad   tencentAdvert.Config
	TencentShAd    tencentAdvert.Config
	TencentSh303Ad tencentAdvert.Config
	TencentSh399Ad tencentAdvert.Config
	TencentSh163Ad tencentAdvert.Config
	TencentSh439Ad tencentAdvert.Config
	TencentSh440Ad tencentAdvert.Config
	UserHost       string
	VmHost         string
}

func InitApiConfig(ayn *ApiConfig, dataId, group string, operType int) {
	var data string
	defer func() {
		err := recover()
		if err != nil {
			logger.E(fmt.Sprintf("%s config init Error", dataId))
			panic(err)
		}
	}()

	data, _ = nacos.GetString(dataId, group, func(data *string, err error) {
		if err == nil {
			loadApiConfig(operType, *data, dataId, ayn)
		}
	})
	if data == "" {
		panic(fmt.Errorf("%s config is empty", dataId))
	}
	loadApiConfig(operType, data, dataId, ayn)
}

func loadApiConfig(operType int, data, dataId string, ayn *ApiConfig) {
	if operType == 0 {
		err := conf.LoadFromYamlBytes([]byte(data), ayn)
		if err != nil {
			panic(fmt.Errorf("%s config Yaml Error %s", dataId, err))
		}
	}
}
