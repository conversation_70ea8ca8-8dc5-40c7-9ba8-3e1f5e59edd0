package buriedPointConfig

import (
	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func EditBuriedPointConfigHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.EditBuriedPointConfigReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := buriedPointConfig.NewEditBuriedPointConfigLogic(r.Context(), svcCtx)
		err := l.EditBuriedPointConfig(&req)
		result.HttpR<PERSON>ult(r, w, result.<PERSON><PERSON><PERSON><PERSON>{}, err)
	}
}
