// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhBuriedPointConfigFieldNames          = builder.RawFieldNames(&VhBuriedPointConfig{})
	vhBuriedPointConfigRows                = strings.Join(vhBuriedPointConfigFieldNames, ",")
	vhBuriedPointConfigRowsExpectAutoSet   = strings.Join(stringx.Remove(vhBuriedPointConfigFieldNames, "`id`", "`created_at`", "`create_time`", "`update_at`", "`updated_at`", "`update_time`", "`create_at`"), ",")
	vhBuriedPointConfigRowsWithPlaceHolder = strings.Join(stringx.Remove(vhBuriedPointConfigFieldNames, "`id`", "`created_at`", "`create_time`", "`update_at`", "`updated_at`", "`update_time`", "`create_at`"), "=?,") + "=?"
)

type (
	vhBuriedPointConfigModel interface {
		Insert(ctx context.Context, data *VhBuriedPointConfig) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhBuriedPointConfig, error)
		FindOneRegionsId(ctx context.Context, id int64) (*VhBuriedPointConfig, error)
		Update(ctx context.Context, data *VhBuriedPointConfig) error
		Delete(ctx context.Context, id int64) error
	}

	defaultVhBuriedPointConfigModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhBuriedPointConfig struct {
		Id          int64  `db:"id"`
		Genre       int64  `db:"genre"`        // 类型（1广告，2商品，3其他）
		Channel     int64  `db:"channel"`      // 频道（0 开屏 1弹窗 2 首页 3 闪购 4 秒发 5 社区 6 兔头 7 个人中心 8 商家秒发）
		RegionId    int64  `db:"region_id"`    // 区域id
		RegionName  string `db:"region_name"`  // 区域名称
		CreatedTime int64  `db:"created_time"` // 创建时间
	}
)

func newVhBuriedPointConfigModel(conn sqlx.SqlConn) *defaultVhBuriedPointConfigModel {
	return &defaultVhBuriedPointConfigModel{
		conn:  conn,
		table: "`vh_buried_point_config`",
	}
}

func (m *defaultVhBuriedPointConfigModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhBuriedPointConfigModel) FindOne(ctx context.Context, id int64) (*VhBuriedPointConfig, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhBuriedPointConfigRows, m.table)
	var resp VhBuriedPointConfig
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBuriedPointConfigModel) FindOneRegionsId(ctx context.Context, id int64) (*VhBuriedPointConfig, error) {
	query := fmt.Sprintf("select %s from %s where `region_id` = ? limit 1", vhBuriedPointConfigRows, m.table)
	var resp VhBuriedPointConfig
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBuriedPointConfigModel) Insert(ctx context.Context, data *VhBuriedPointConfig) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, vhBuriedPointConfigRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Genre, data.Channel, data.RegionId, data.RegionName, data.CreatedTime)
	return ret, err
}

func (m *defaultVhBuriedPointConfigModel) Update(ctx context.Context, data *VhBuriedPointConfig) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhBuriedPointConfigRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Genre, data.Channel, data.RegionId, data.RegionName, data.CreatedTime, data.Id)
	return err
}

func (m *defaultVhBuriedPointConfigModel) tableName() string {
	return m.table
}
