package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhSlientStatisticsPvModel = (*customVhSlientStatisticsPvModel)(nil)

type (
	// VhSlientStatisticsPvModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhSlientStatisticsPvModel.
	VhSlientStatisticsPvModel interface {
		vhSlientStatisticsPvModel
	}

	customVhSlientStatisticsPvModel struct {
		*defaultVhSlientStatisticsPvModel
	}
)

// NewVhSlientStatisticsPvModel returns a model for the database table.
func NewVhSlientStatisticsPvModel(conn sqlx.SqlConn) VhSlientStatisticsPvModel {
	return &customVhSlientStatisticsPvModel{
		defaultVhSlientStatisticsPvModel: newVhSlientStatisticsPvModel(conn),
	}
}
