// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhReportDefaultFieldNames          = builder.RawFieldNames(&VhReportDefault{})
	vhReportDefaultRows                = strings.Join(vhReportDefaultFieldNames, ",")
	vhReportDefaultRowsExpectAutoSet   = strings.Join(stringx.Remove(vhReportDefaultFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhReportDefaultRowsWithPlaceHolder = strings.Join(stringx.Remove(vhReportDefaultFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhReportDefaultModel interface {
		Insert(ctx context.Context, data *VhReportDefault) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhReportDefault, error)
		Update(ctx context.Context, data *VhReportDefault) error
		Delete(ctx context.Context, id int64) error
	}

	defaultVhReportDefaultModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhReportDefault struct {
		Id          int64  `db:"id"`
		Uid         string `db:"uid"`          // 用户id（包括没登陆的用户id）
		IsLogin     int64  `db:"is_login"`     // 是否登陆（1登陆 0未登陆）
		Genre       int64  `db:"genre"`        // 类型（1广告，2商品）
		Channel     int64  `db:"channel"`      // 频道（0 开屏 1弹窗 2 首页 3 闪购 4 秒发 5 社区 6 兔头 7 个人中心 8 商家秒发）
		RegionId    int64  `db:"region_id"`    // 区域id
		ButtonId    int64  `db:"button_id"`    // 按钮id(如果是商品就是商品id 如果是广告就是广告id)
		Client      int64  `db:"client"`       // 客户端（0 iOS 1 安卓 2 小程序 3 h5 4 pc）
		Mid         int64  `db:"mid"`          // 广告对应的商户id(秒发里面)
		CreatedTime int64  `db:"created_time"` // 创建时间
	}
)

func newVhReportDefaultModel(conn sqlx.SqlConn) *defaultVhReportDefaultModel {
	return &defaultVhReportDefaultModel{
		conn:  conn,
		table: "`vh_report_default`",
	}
}

func (m *defaultVhReportDefaultModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhReportDefaultModel) FindOne(ctx context.Context, id int64) (*VhReportDefault, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhReportDefaultRows, m.table)
	var resp VhReportDefault
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhReportDefaultModel) Insert(ctx context.Context, data *VhReportDefault) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhReportDefaultRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.IsLogin, data.Genre, data.Channel, data.RegionId, data.ButtonId, data.Client, data.Mid, data.CreatedTime)
	return ret, err
}

func (m *defaultVhReportDefaultModel) Update(ctx context.Context, data *VhReportDefault) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhReportDefaultRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.IsLogin, data.Genre, data.Channel, data.RegionId, data.ButtonId, data.Client, data.Mid, data.CreatedTime, data.Id)
	return err
}

func (m *defaultVhReportDefaultModel) tableName() string {
	return m.table
}
