syntax = "v1"

info(
    title: "埋点配置"
    author: "gangaoh"
    email: "<EMAIL>"
    version: "v3"
)

type (
    AddBuriedPointConfigReq struct {
        Genre int64 `json:"genre" validate:"min=1" v:"类型"`
        Channel int64 `json:"channel" validate:"min=0" v:"频道"`
        RegionId int64 `json:"region_id" validate:"required" v:"区域id"`
        RegionName string `json:"region_name" validate:"required" v:"区域名称"`
    }

    EditBuriedPointConfigReq struct {
        Id int64 `json:"id" validate:"min=1" v:"配置ID"`
        Genre int64 `json:"genre" validate:"min=1" v:"类型"`
        Channel int64 `json:"channel" validate:"min=0" v:"频道"`
        RegionId int64 `json:"region_id" validate:"required" v:"区域id"`
        RegionName string `json:"region_name" validate:"required" v:"区域名称"`
    }

    BuriedPointConfigListReq struct {
        Page int64 `form:"page,optional"`
        Limit int64 `form:"limit,optional"`
    }
    BuriedPointConfigListResp struct {
        List []BuriedPointConfigListItem `json:"list"`
        Total int64 `json:"total"`
    }
    BuriedPointConfigListItem struct {
        Id int64 `json:"id"`
        Genre int64 `json:"genre"`
        GenreName string `json:"genre_name"`
        Channel int64 `json:"channel"`
        ChannelName string `json:"channel_name"`
        RegionId int64 `json:"region_id"`
        RegionName string `json:"region_name"`
        CreatedTime string `json:"created_time"`
    }

    BuriedPointConfigFilterResp struct {
        channels []BuriedPointConfigFilterRespChannels `json:"channels"`
    }
    BuriedPointConfigFilterRespChannels struct {
        ChannelId int64 `json:"channelId"`
        ChannelName string `json:"channelName"`
        Regions []BuriedPointConfigFilterRespRegion `json:"regions"`
    }
    BuriedPointConfigFilterRespRegion struct {
        RegionsId int64 `json:"regionsId"`
        RegionName string `json:"regionName"`
    }
    
    BuriedPointConfigFilterButtonReq struct {
        RegionsId int64 `form:"regionsId" validate:"required" v:"区域ID"`
    }
    BuriedPointConfigFilterButtonResp struct {
        button []BuriedPointConfigFilterRespButton `json:"button"`
    }
    BuriedPointConfigFilterRespButton struct {
        ButtonId int64 `json:"buttonId"`
        ButtonName string `json:"buttonName"`
    }

    BuriedPointConfigGenreResp struct {
        Genres []BuriedPointConfigGenreRespGenre `json:"genres"`
    }
    BuriedPointConfigGenreRespGenre struct {
        GenreId int64 `json:"genreId"`
        GenreName string `json:"genreName"`
    }

    BuriedPointConfigChannelResp struct {
        Channels []BuriedPointConfigChannelRespChannel `json:"channels"`
    }
    BuriedPointConfigChannelRespChannel struct {
        ChannelId int64 `json:"channelId"`
        ChannelName string `json:"channelName"`
    }
)

@server(
    group : buriedPointConfig
    prefix :/maidian/v3/buriedPointConfig
)

service maidian {
    @handler AddBuriedPointConfig //添加埋点配置
    post /add (AddBuriedPointConfigReq)

    @handler EditBuriedPointConfig //编辑埋点配置
    post /edit (EditBuriedPointConfigReq)

    @handler BuriedPointConfigList //埋点配置列表
    get /list (BuriedPointConfigListReq) returns (BuriedPointConfigListResp)

    @handler BuriedPointConfigChannel //埋点配置频道
    get /channel returns (BuriedPointConfigChannelResp)

    @handler BuriedPointConfigGenre //埋点配置类型
    get /genre returns (BuriedPointConfigGenreResp)

    @handler BuriedPointConfigFilter //埋点配置筛选项
    get /filter returns (BuriedPointConfigFilterResp)

    @handler BuriedPointConfigFilterButton //埋点配置筛选项按钮
    get /filterbutton (BuriedPointConfigFilterButtonReq) returns (BuriedPointConfigFilterButtonResp)

}