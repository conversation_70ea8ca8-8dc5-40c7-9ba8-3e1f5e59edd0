syntax = "v1"

info(
    title: "字节广告上报"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    ByteDanceCallbackReq struct {
        Callback string `form:"callback"`
        Oaid string `form:"oaid,optional"`  //安卓oaid
        Imei string `form:"imei,optional"`  //安卓imei
        Idfa string `form:"idfa,optional"`  //苹果idfa
        Os int64 `form:"os,range=[0:1]"`    //0安卓1苹果3其他
    }

    ByteDanceReportReq struct {
        Oaid string `json:"oaid,optional"`  //安卓oaid
        Imei string `json:"imei,optional"`  //安卓imei
        Idfa string `json:"idfa,optional"`  //苹果idfa
        Os int64 `json:"os,range=[0:1]"`    //0安卓1苹果3其他
        EventType string `json:"event_type"`
        EventWeight float64 `json:"event_weight,default=0"`
    }
)

@server(
    //middleware: User
    group : byteDance
    prefix :/maidian/v3/byteDance
)


service maidian {
    @handler Callback //点击监测
    get /callback (ByteDanceCallbackReq)

    @handler Report //上报
    post /report (ByteDanceReportReq)
}