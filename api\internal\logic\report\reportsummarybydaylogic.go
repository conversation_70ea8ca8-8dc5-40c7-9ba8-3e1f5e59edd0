package report

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportSummaryByDayLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportSummaryByDayLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportSummaryByDayLogic {
	return &ReportSummaryByDayLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportSummaryByDayLogic) ReportSummaryByDay(req *types.ReportSummaryByDayReq) (resp *types.ReportSummaryByDayResp, err error) {
	where := squirrel.And{squirrel.Eq{"channel": req.ChannelId}}
	d := strings.Split(req.Date, "-")
	if len(d) != 3 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "日期格式有误")
	}
	t, _ := time.ParseInLocation("2006-01-02", req.Date, time.Local)
	startTime := t.Unix()
	//startTime := time.Date(cast.ToInt(d[0]), time.Month(cast.ToInt(d[1])), 8, 0, 0, 0, 0, time.Local).Unix()
	endTime := startTime + 86399
	where = append(where, squirrel.GtOrEq{"created_time": startTime}, squirrel.LtOrEq{"created_time": endTime})
	if req.RegionId != 0 {
		where = append(where, squirrel.Eq{"region_id": req.RegionId})
	}
	if req.ButtonId != 0 {
		where = append(where, squirrel.Eq{"button_id": req.ButtonId})
	}
	builder := squirrel.Select("uid,FROM_UNIXTIME(created_time,'%H') as h").
		From(l.svcCtx.ReportModel.GetTable()).Where(where).OrderBy("created_time asc")

	var result []*model.VhReportSummaryDay
	err = l.svcCtx.ReportModel.FindRows(l.ctx, builder, &result)
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.ReportSummaryByDayResp)
	if len(result) > 0 {
		var nullStruct struct{}
		currentH := int64(0)
		currentUids := map[string]struct{}{}
		for _, data := range result {
			if currentH != data.H {
				if len(currentUids) > 0 {
					add(resp, currentH, false, int64(len(currentUids)))
				}
				currentH = data.H
				for x := range currentUids {
					delete(currentUids, x)
				}
			}
			add(resp, data.H, true, 1)
			currentUids[data.Uid] = nullStruct
		}
		if len(currentUids) > 0 {
			add(resp, currentH, false, int64(len(currentUids)))
		}
	}

	return resp, nil
}

func add(resp *types.ReportSummaryByDayResp, h int64, isPv bool, num int64) {
	switch h {
	case 0:
		if isPv {
			resp.One.Pv++
		} else {
			resp.One.Uv += num
		}
		break
	case 1:
		if isPv {
			resp.Two.Pv++
		} else {
			resp.Two.Uv += num
		}
		break
	case 2:
		if isPv {
			resp.Three.Pv++
		} else {
			resp.Three.Uv += num
		}
		break
	case 3:
		if isPv {
			resp.Four.Pv++
		} else {
			resp.Four.Uv += num
		}
		break
	case 4:
		if isPv {
			resp.Five.Pv++
		} else {
			resp.Five.Uv += num
		}
		break
	case 5:
		if isPv {
			resp.Six.Pv++
		} else {
			resp.Six.Uv += num
		}
		break
	case 6:
		if isPv {
			resp.Seven.Pv++
		} else {
			resp.Seven.Uv += num
		}
		break
	case 7:
		if isPv {
			resp.Eight.Pv++
		} else {
			resp.Eight.Uv += num
		}
		break
	case 8:
		if isPv {
			resp.Nine.Pv++
		} else {
			resp.Nine.Uv += num
		}
		break
	case 9:
		if isPv {
			resp.Ten.Pv++
		} else {
			resp.Ten.Uv += num
		}
		break
	case 10:
		if isPv {
			resp.Eleven.Pv++
		} else {
			resp.Eleven.Uv += num
		}
		break
	case 11:
		if isPv {
			resp.Twelve.Pv++
		} else {
			resp.Twelve.Uv += num
		}
		break
	case 12:
		if isPv {
			resp.Thirteen.Pv++
		} else {
			resp.Thirteen.Uv += num
		}
		break
	case 13:
		if isPv {
			resp.Fourteen.Pv++
		} else {
			resp.Fourteen.Uv += num
		}
		break
	case 14:
		if isPv {
			resp.Fifteen.Pv++
		} else {
			resp.Fifteen.Uv += num
		}
		break
	case 15:
		if isPv {
			resp.Sixteen.Pv++
		} else {
			resp.Sixteen.Uv += num
		}
		break
	case 16:
		if isPv {
			resp.Seventeen.Pv++
		} else {
			resp.Seventeen.Uv += num
		}
		break
	case 17:
		if isPv {
			resp.Eighteen.Pv++
		} else {
			resp.Eighteen.Uv += num
		}
		break
	case 18:
		if isPv {
			resp.Nineteen.Pv++
		} else {
			resp.Nineteen.Uv += num
		}
		break
	case 19:
		if isPv {
			resp.Twenty.Pv++
		} else {
			resp.Twenty.Uv += num
		}
		break
	case 20:
		if isPv {
			resp.TwentyOne.Pv++
		} else {
			resp.TwentyOne.Uv += num
		}
		break
	case 21:
		if isPv {
			resp.TwentyTwo.Pv++
		} else {
			resp.TwentyTwo.Uv += num
		}
		break
	case 22:
		if isPv {
			resp.TwentyThree.Pv++
		} else {
			resp.TwentyThree.Uv += num
		}
		break
	case 23:
		if isPv {
			resp.TwentyFour.Pv++
		} else {
			resp.TwentyFour.Uv += num
		}
		break
	}
}
