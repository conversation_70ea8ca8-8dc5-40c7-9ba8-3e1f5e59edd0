package svc

import (
	"engine/api/internal/middleware"
	"engine/api/internal/task"
	cf "engine/common/config"
	"engine/common/esClient"
	"engine/common/model"
	"engine/common/tencentAdvert"
	"engine/common/validation"
	victoriaMe "engine/common/victoriaMetrics"
	"time"

	"github.com/go-resty/resty/v2"
	red "github.com/gomodule/redigo/redis"
	"github.com/olivere/elastic/v7"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
)

type (
	ServiceContext struct {
		Config                 cf.ApiConfig
		Verify                 *validation.Verify
		User                   rest.Middleware
		EsClient               *elastic.Client
		ReportModel            model.VhReportDefaultModel
		BuriedPointConfigModel model.VhBuriedPointConfigModel
		TencentMomentsLogModel model.VhAuctionTencentMomentsLogModel
		ClickCallbackModel     model.VhClickCallbackModel
		SilentStatisticsUser   model.VhSilentStatisticsUserModel
		SilentStatisticsOrder  model.VhSilentStatisticsOrderModel
		SilentStatisticsPv     model.VhSlientStatisticsPvModel
		CouponIssueModel       model.VhCouponIssueModel
		WasmModel              model.VhWasmLogModel
		Redis                  *red.Pool
		Ads                    *tencentAdvert.Ads
		Ads264                 *tencentAdvert.Ads
		Ads389                 *tencentAdvert.Ads
		ShAds                  *tencentAdvert.Ads
		Sh303Ads               *tencentAdvert.Ads
		Sh399Ads               *tencentAdvert.Ads
		Sh163Ads               *tencentAdvert.Ads
		Sh439Ads               *tencentAdvert.Ads
		Sh440Ads               *tencentAdvert.Ads
		RestyHttp              *resty.Client
		Metrics                *victoriaMe.MetricsClient
	}
)

func NewServiceContext(c cf.ApiConfig) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.Mysql.DataSource)
	marketingSqlConn := sqlx.NewMysql(c.MarketingMysql.DataSource)
	//newEsClient, err := esClient.NewEsClient("http://es-cn-7mz2retry0008zrzt.public.elasticsearch.aliyuncs.com:9200", "elastic", "BYI8uuJGQuo45smj")
	newEsClient, err := esClient.NewEsClient(c.Es.Host, c.Es.Username, c.Es.Password)
	if err != nil {
		panic(err)
	}
	rd := &red.Pool{
		MaxIdle:   5,
		MaxActive: 200,
		Dial: func() (red.Conn, error) {
			//return red.Dial("tcp", "r-8vbyf9qn03iwmy7w5npd.redis.zhangbei.rds.aliyuncs.com:6379", red.DialPassword("NRDSYa5e6EWZuJ3d"),
			return red.Dial("tcp", c.Redis.Host, red.DialPassword(c.Redis.Pass),
				red.DialDatabase(15),                //拍卖在11
				red.DialConnectTimeout(time.Second), //链接超时
				red.DialWriteTimeout(time.Second),   //写超时
				red.DialReadTimeout(time.Second),    //读超时
			)
		},
	}
	srv := &ServiceContext{
		Config:                 c,
		Verify:                 validation.NewVerify(),
		User:                   middleware.NewUserMiddleware().Handle,
		EsClient:               newEsClient,
		Redis:                  rd,
		ReportModel:            model.NewVhReportDefaultModel(sqlConn),
		BuriedPointConfigModel: model.NewVhBuriedPointConfigModel(sqlConn),
		TencentMomentsLogModel: model.NewVhAuctionTencentMomentsLogModel(sqlConn),
		ClickCallbackModel:     model.NewVhClickCallbackModel(sqlConn),
		SilentStatisticsUser:   model.NewVhSilentStatisticsUserModel(sqlConn),
		SilentStatisticsOrder:  model.NewVhSilentStatisticsOrderModel(sqlConn),
		SilentStatisticsPv:     model.NewVhSlientStatisticsPvModel(sqlConn),
		CouponIssueModel:       model.NewVhCouponIssueModel(marketingSqlConn),
		WasmModel:              model.NewVhWasmLogModel(sqlConn),
		RestyHttp:              resty.New(),
		Ads:                    tencentAdvert.GetAds(&c.TencentAd, c.Mode),
		ShAds:                  tencentAdvert.GetAds(&c.TencentShAd, c.Mode),
		Sh303Ads:               tencentAdvert.GetAds(&c.TencentSh303Ad, c.Mode),
		Sh399Ads:               tencentAdvert.GetAds(&c.TencentSh399Ad, c.Mode),
		Ads264:                 tencentAdvert.GetAds(&c.Tencent264Ad, c.Mode),
		Ads389:                 tencentAdvert.GetAds(&c.Tencent389Ad, c.Mode),
		Sh163Ads:               tencentAdvert.GetAds(&c.TencentSh163Ad, c.Mode),
		Sh439Ads:               tencentAdvert.GetAds(&c.TencentSh439Ad, c.Mode),
		Sh440Ads:               tencentAdvert.GetAds(&c.TencentSh440Ad, c.Mode),
		//Metrics:                victoriaMe.NewMetricsClient(rd, "https://callback.vinehoo.com/victoria-metrics", "vinehoodev", "vinehoo666"),
		Metrics: victoriaMe.NewMetricsClient(rd, c.VmHost, "vinehoodev", "vinehoo666"),
	}

	go task.StartTask(&task.RankedProduct{
		BaseTask: task.BaseTask{Interval: 5 * time.Second},
		Metrics:  srv.Metrics,
		EsClient: srv.EsClient,
		Redis:    srv.Redis,
	})

	return srv
}
