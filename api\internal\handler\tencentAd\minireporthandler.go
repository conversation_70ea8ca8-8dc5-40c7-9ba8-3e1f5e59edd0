package tencentAd

import (
	"engine/api/internal/logic/tencentAd"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func MiniReportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.TencentAdMiniReportReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := tencentAd.NewMiniReportLogic(r.Context(), svcCtx)
		err := l.MiniReport(&req)
		result.HttpResult(r, w, result.<PERSON>ull<PERSON>son{}, err)
	}
}
