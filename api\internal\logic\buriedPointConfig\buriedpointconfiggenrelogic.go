package buriedPointConfig

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type BuriedPointConfigGenreLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBuriedPointConfigGenreLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BuriedPointConfigGenreLogic {
	return &BuriedPointConfigGenreLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BuriedPointConfigGenreLogic) BuriedPointConfigGenre() (resp *types.BuriedPointConfigGenreResp, err error) {
	var list []VinehooButton
	result := types.BuriedPointConfigGenreResp{
		Genres: make([]types.BuriedPointConfigGenreRespGenre, 0),
	}
	resp = &result
	builder := squirrel.Select("id,name as title").From("vh_buried_point_genre")
	l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
	for _, v := range list {
		result.Genres = append(result.Genres, types.BuriedPointConfigGenreRespGenre{
			GenreId:   v.Id,
			GenreName: v.Title,
		})
	}
	return
}
