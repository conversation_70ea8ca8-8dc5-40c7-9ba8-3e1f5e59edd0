package report

import (
	"encoding/json"
	"engine/api/internal/logic/report"
	"engine/api/internal/svc"
	"engine/common/result"
	"errors"
	"io"
	"net/http"
)

func VmAnyReportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req map[string]interface{}
		body, err := io.ReadAll(r.Body)
		if err != nil {
			result.ParamErrorResult(r, w, errors.New("读取请求体失败"))
			return
		}
		if err = json.Unmarshal(body, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := report.NewVmAnyReportLogic(r.Context(), svcCtx)
		err = l.VmAnyReport(req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
