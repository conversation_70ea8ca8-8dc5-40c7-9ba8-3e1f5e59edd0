package report

import (
	"context"
	"engine/common/xerr"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"

	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportSummaryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportSummaryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportSummaryLogic {
	return &ReportSummaryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportSummaryLogic) ReportSummary(req *types.ReportSummaryReq) (resp *types.ReportSummaryResp, err error) {
	var (
		button_ids     []int64
		channelResp    *types.BuriedPointConfigFilterResp
		req_button_ids []int64
		buttonNames    []string
		channelName    string
		regionName     string
	)
	type ButtonList struct {
		ChannelId int64 `json:"channelId"`
		RegionId  int64 `json:"regionId"`
		ButtonId  int64 `json:"buttonId"`
	}

	button_list := make([]ButtonList, 0)

	channelLogic := buriedPointConfig.NewBuriedPointConfigFilterLogic(l.ctx, l.svcCtx)
	channelResp, err = channelLogic.BuriedPointConfigFilter()
	if err != nil {
		return nil, err
	}
	if req.ButtonId != "" {
		ButtonId := strings.Split(req.ButtonId, ",")
		for _, bid := range ButtonId {
			// bid转int64
			bid, err := strconv.ParseInt(bid, 10, 64)
			if err != nil {
				return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "ButtonId格式有误")
			}
			button_ids = append(button_ids, bid)
		}
		req_button_ids = button_ids
	}

	// 获取当前频道的区域列表
	var currentChannel *types.BuriedPointConfigFilterRespChannels
	for _, channel := range channelResp.Channels {
		if channel.ChannelId == req.ChannelId {
			currentChannel = &channel
			break
		}
	}

	if currentChannel != nil {
		channelName = currentChannel.ChannelName

		// 获取所有区域的按钮
		for _, region := range currentChannel.Regions {
			// 如果指定了区域ID，只处理指定的区域
			if req.RegionId > 0 && region.RegionsId == req.RegionId {
				regionName = region.RegionName
			}
		}
		if len(req_button_ids) > 0 {
			var buttonResp *types.BuriedPointConfigFilterButtonResp
			buttonReq := types.BuriedPointConfigFilterButtonReq{
				RegionsId: req.RegionId,
			}
			buttonLogic := buriedPointConfig.NewBuriedPointConfigFilterButtonLogic(l.ctx, l.svcCtx)
			buttonResp, err := buttonLogic.BuriedPointConfigFilterButton(&buttonReq)
			if err != nil {
				return nil, xerr.NewErrMsg(err.Error())
			}

			for _, button := range buttonResp.Button {
				for _, bid := range req_button_ids {
					if button.ButtonId == bid {
						if button.ButtonName == "" {
							button.ButtonName = fmt.Sprintf("%d", button.ButtonId)
						}
						buttonNames = append(buttonNames, button.ButtonName)
						button_list = append(button_list, ButtonList{
							ChannelId: req.ChannelId,
							RegionId:  req.RegionId,
							ButtonId:  button.ButtonId,
						})
						break
					}
				}
			}
		} else if req.RegionId > 0 {
			buttonNames = append(buttonNames, regionName)
		} else {
			buttonNames = append(buttonNames, channelName)
		}
	}

	// 解析日期
	startTime, err := time.ParseInLocation("2006-01-02", req.StartDate, time.Local)
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "开始日期格式有误")
	}
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", fmt.Sprintf("%s 23:59:59", req.EndDate), time.Local)
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "结束日期格式有误")
	}

	// 构建查询条件
	where := squirrel.And{
		squirrel.Eq{"channel": req.ChannelId},
		squirrel.GtOrEq{"created_time": startTime.Unix()},
		squirrel.LtOrEq{"created_time": endTime.Unix()},
	}
	if req.RegionId > 0 {
		where = append(where, squirrel.Eq{"region_id": req.RegionId})
	}
	if len(req_button_ids) > 0 {
		where = append(where, squirrel.Eq{"button_id": req_button_ids})
	}

	// 查询数据
	builder := squirrel.Select("uid, FROM_UNIXTIME(created_time,'%Y-%m-%d') as date, channel, region_id, button_id, is_login").
		From(l.svcCtx.ReportModel.GetTable()).
		Where(where).
		OrderBy("created_time asc")

	type Result struct {
		Uid      string `db:"uid"`
		Date     string `db:"date"`
		Channel  int64  `db:"channel"`
		RegionId int64  `db:"region_id"`
		ButtonId int64  `db:"button_id"`
		IsLogin  int64  `db:"is_login"`
	}
	var result []*Result
	err = l.svcCtx.ReportModel.FindRows(l.ctx, builder, &result)
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 处理数据
	resp = new(types.ReportSummaryResp)
	resp.Header = append([]string{"日期"}, buttonNames...)

	// 生成所有日期
	allDates := make([]string, 0)
	currentDate := startTime
	for !currentDate.After(endTime) {
		allDates = append(allDates, currentDate.Format("2006-01-02"))
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	// 按日期和按钮ID分组统计
	type Stats struct {
		pv int64
		uv map[string]struct{}
	}
	dateButtonStats := make(map[string]Stats)

	// 初始化统计结构
	for _, date := range allDates {
		key := fmt.Sprintf("%s:%d", date, req.ChannelId)
		if len(button_list) > 0 {
			for _, v := range button_list {
				key = fmt.Sprintf("%s:%d:%d:%d", date, v.ChannelId, v.RegionId, v.ButtonId)
			}
		} else if req.RegionId > 0 {
			key = fmt.Sprintf("%s:%d:%d", date, req.ChannelId, req.RegionId)
		}
		dateButtonStats[key] = Stats{
			uv: make(map[string]struct{}),
		}
	}

	// 统计数据
	for _, r := range result {
		key := fmt.Sprintf("%s:%d", r.Date, r.Channel)
		if len(button_list) > 0 {
			key = fmt.Sprintf("%s:%d:%d:%d", r.Date, r.Channel, r.RegionId, r.ButtonId)
		} else if req.RegionId > 0 {
			key = fmt.Sprintf("%s:%d:%d", r.Date, r.Channel, r.RegionId)
		}
		if stats, exists := dateButtonStats[key]; exists {
			stats.pv++
			if r.IsLogin == 1 {
				stats.uv[r.Uid] = struct{}{}
			}
			dateButtonStats[key] = stats
		} else {
			stats := Stats{
				pv: 1,
				uv: make(map[string]struct{}),
			}
			if r.IsLogin == 1 {
				stats.uv[r.Uid] = struct{}{}
			}
			dateButtonStats[key] = stats
		}
	}

	// 生成响应数据
	for _, date := range allDates {
		// 预分配切片大小
		pvRow := make([]interface{}, 0)
		uvRow := make([]interface{}, 0)
		pvRow = append(pvRow, date)
		uvRow = append(uvRow, date)

		if len(button_list) > 0 {
			// 填充统计数据
			for _, v := range button_list {
				var (
					pv int64
					uv int64
				)
				key := fmt.Sprintf("%s:%d:%d:%d", date, v.ChannelId, v.RegionId, v.ButtonId)
				if stats, exists := dateButtonStats[key]; exists {
					pv = stats.pv
					uv = int64(len(stats.uv))
				}
				pvRow = append(pvRow, pv)
				uvRow = append(uvRow, uv)
			}
		} else {
			var (
				pv int64
				uv int64
			)
			key := fmt.Sprintf("%s:%d", date, req.ChannelId)
			if req.RegionId > 0 {
				key = fmt.Sprintf("%s:%d:%d", date, req.ChannelId, req.RegionId)
			}
			if stats, exists := dateButtonStats[key]; exists {
				pv = stats.pv
				uv = int64(len(stats.uv))
			}
			pvRow = append(pvRow, pv)
			uvRow = append(uvRow, uv)
		}

		resp.PV = append(resp.PV, pvRow)
		resp.UV = append(resp.UV, uvRow)
	}

	return resp, nil
}
