// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhSlientStatisticsPvFieldNames          = builder.RawFieldNames(&VhSlientStatisticsPv{})
	vhSlientStatisticsPvRows                = strings.Join(vhSlientStatisticsPvFieldNames, ",")
	vhSlientStatisticsPvRowsExpectAutoSet   = strings.Join(stringx.Remove(vhSlientStatisticsPvFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhSlientStatisticsPvRowsWithPlaceHolder = strings.Join(stringx.Remove(vhSlientStatisticsPvFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhSlientStatisticsPvModel interface {
		Insert(ctx context.Context, data *VhSlientStatisticsPv) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhSlientStatisticsPv, error)
		Update(ctx context.Context, data *VhSlientStatisticsPv) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhSlientStatisticsPvModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhSlientStatisticsPv struct {
		Id      int64 `db:"id"`
		Genre   int64 `db:"genre"` // 1访问量 2添加客户量
		Uid     int64 `db:"uid"`
		AddTime int64 `db:"add_time"`
	}
)

func newVhSlientStatisticsPvModel(conn sqlx.SqlConn) *defaultVhSlientStatisticsPvModel {
	return &defaultVhSlientStatisticsPvModel{
		conn:  conn,
		table: "`vh_slient_statistics_pv`",
	}
}

func (m *defaultVhSlientStatisticsPvModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhSlientStatisticsPvModel) FindOne(ctx context.Context, id int64) (*VhSlientStatisticsPv, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhSlientStatisticsPvRows, m.table)
	var resp VhSlientStatisticsPv
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhSlientStatisticsPvModel) Insert(ctx context.Context, data *VhSlientStatisticsPv) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, vhSlientStatisticsPvRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Genre, data.Uid, data.AddTime)
	return ret, err
}

func (m *defaultVhSlientStatisticsPvModel) Update(ctx context.Context, data *VhSlientStatisticsPv) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhSlientStatisticsPvRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Genre, data.Uid, data.AddTime, data.Id)
	return err
}

func (m *defaultVhSlientStatisticsPvModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhSlientStatisticsPvRows).From(m.table)
}

func (m *defaultVhSlientStatisticsPvModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhSlientStatisticsPvModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhSlientStatisticsPvModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhSlientStatisticsPvModel) TableName() string {
	return m.table
}
