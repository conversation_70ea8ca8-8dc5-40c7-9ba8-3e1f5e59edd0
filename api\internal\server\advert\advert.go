package advert

import (
	"engine/api/internal/config"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
)

type (
	ReqInfo struct {
		Module string  `json:"module"`
		Terms  []*Item `json:"terms"`
	}
	Item struct {
		Id   int64  `json:"id"`
		Nums uint32 `json:"nums"`
	}
)

func ViewNums(req *ReqInfo, advertServer string) error {
	client := resty.New()
	response := &config.AdvertViewNumsResp{}
	_, err := client.R().SetBody([]*ReqInfo{req}).
		SetResult(response).Post(advertServer + config.ApiAdvertViewNums)
	if err != nil {
		return err
	}
	if response.ErrorCode != 0 {
		return errors.New(response.ErrorMsg)
	}

	return nil
}
