package baidu

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CallbackLogic {
	return &CallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CallbackLogic) Callback(req *types.BaiduCallbackReq) error {
	builder := model.CountBuilder("*", l.svcCtx.ClickCallbackModel.TableName())
	if req.Os == 2 {
		req.Os = 0
	} else {
		req.Os = 1
	}
	builder = builder.Where(squirrel.And{squirrel.Eq{"genre": model.GenreBaidu, "os": req.Os, "oaid": req.Oaid, "imei": req.ImeiMd5, "idfa": req.Idfa}})
	count, err := l.svcCtx.ClickCallbackModel.FindCount(l.ctx, builder)
	if err != nil {
		logx.Error(fmt.Sprintf("CallbackLogic baidu ClickCallbackModel FindCount err %s", err.Error()))
		return xerr.NewErrCode(xerr.DbError)
	}

	if count == 0 {
		_, err := l.svcCtx.ClickCallbackModel.Insert(l.ctx, &model.VhClickCallback{
			Os:       req.Os,
			Genre:    model.GenreBaidu,
			Oaid:     req.Oaid,
			Imei:     req.ImeiMd5,
			Idfa:     req.Idfa,
			Callback: req.CallbackUrl,
			Akey:     req.Akey,
		})
		if err != nil {
			logx.Error(fmt.Sprintf("CallbackLogic baidu ClickCallbackModel Insert err %s", err.Error()))
			return xerr.NewErrCode(xerr.DbError)
		}
	}
	return nil
}
