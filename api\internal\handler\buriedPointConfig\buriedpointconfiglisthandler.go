package buriedPointConfig

import (
	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func BuriedPointConfigListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BuriedPointConfigListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := buriedPointConfig.NewBuriedPointConfigListLogic(r.Context(), svcCtx)
		resp, err := l.BuriedPointConfigList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
