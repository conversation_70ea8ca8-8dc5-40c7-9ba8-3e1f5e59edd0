package report

import (
	"engine/api/internal/logic/report"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ReportButtonByRegionListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ReportButtonByRegionListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := report.NewReportButtonByRegionListLogic(r.Context(), svcCtx)
		resp, err := l.ReportButtonByRegionList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
