package model

import (
	"context"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhCouponIssueModel = (*customVhCouponIssueModel)(nil)

type (
	// VhCouponIssueModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhCouponIssueModel.
	VhCouponIssueModel interface {
		vhCouponIssueModel
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
	}

	customVhCouponIssueModel struct {
		*defaultVhCouponIssueModel
	}
)

// NewVhCouponIssueModel returns a model for the database table.
func NewVhCouponIssueModel(conn sqlx.SqlConn) VhCouponIssueModel {
	return &customVhCouponIssueModel{
		defaultVhCouponIssueModel: newVhCouponIssueModel(conn),
	}
}

func (m *defaultVhCouponIssueModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}
	err = m.conn.QueryRowCtx(ctx, resp, query, values...)
	switch err {
	case sqlc.ErrNotFound:
		return ErrNotFound
	default:
		return err
	}
}
