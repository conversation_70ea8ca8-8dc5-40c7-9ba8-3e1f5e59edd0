// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhSilentStatisticsOrderFieldNames          = builder.RawFieldNames(&VhSilentStatisticsOrder{})
	vhSilentStatisticsOrderRows                = strings.Join(vhSilentStatisticsOrderFieldNames, ",")
	vhSilentStatisticsOrderRowsExpectAutoSet   = strings.Join(stringx.Remove(vhSilentStatisticsOrderFieldNames, "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhSilentStatisticsOrderRowsWithPlaceHolder = strings.Join(stringx.Remove(vhSilentStatisticsOrderFieldNames, "`main_order_no`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhSilentStatisticsOrderModel interface {
		Insert(ctx context.Context, data *VhSilentStatisticsOrder) (sql.Result, error)
		FindOne(ctx context.Context, mainOrderNo string) (*VhSilentStatisticsOrder, error)
		Update(ctx context.Context, data *VhSilentStatisticsOrder) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, mainOrderNo string) error
	}

	defaultVhSilentStatisticsOrderModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhSilentStatisticsOrder struct {
		MainOrderNo string  `db:"main_order_no"` // 主订单号
		Uid         int64   `db:"uid"`           // 用户id
		UseCouponId int64   `db:"use_coupon_id"` // 优惠劵发放记录id(0代表没使用优惠劵)
		PayMoney    float64 `db:"pay_money"`     // 支付金额
		PayTime     int64   `db:"pay_time"`      // 支付时间
	}
)

func newVhSilentStatisticsOrderModel(conn sqlx.SqlConn) *defaultVhSilentStatisticsOrderModel {
	return &defaultVhSilentStatisticsOrderModel{
		conn:  conn,
		table: "`vh_silent_statistics_order`",
	}
}

func (m *defaultVhSilentStatisticsOrderModel) Delete(ctx context.Context, mainOrderNo string) error {
	query := fmt.Sprintf("delete from %s where `main_order_no` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, mainOrderNo)
	return err
}

func (m *defaultVhSilentStatisticsOrderModel) FindOne(ctx context.Context, mainOrderNo string) (*VhSilentStatisticsOrder, error) {
	query := fmt.Sprintf("select %s from %s where `main_order_no` = ? limit 1", vhSilentStatisticsOrderRows, m.table)
	var resp VhSilentStatisticsOrder
	err := m.conn.QueryRowCtx(ctx, &resp, query, mainOrderNo)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhSilentStatisticsOrderModel) Insert(ctx context.Context, data *VhSilentStatisticsOrder) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, vhSilentStatisticsOrderRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.MainOrderNo, data.Uid, data.UseCouponId, data.PayMoney, data.PayTime)
	return ret, err
}

func (m *defaultVhSilentStatisticsOrderModel) Update(ctx context.Context, data *VhSilentStatisticsOrder) error {
	query := fmt.Sprintf("update %s set %s where `main_order_no` = ?", m.table, vhSilentStatisticsOrderRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.UseCouponId, data.PayMoney, data.PayTime, data.MainOrderNo)
	return err
}

func (m *defaultVhSilentStatisticsOrderModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhSilentStatisticsOrderRows).From(m.table)
}

func (m *defaultVhSilentStatisticsOrderModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhSilentStatisticsOrderModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhSilentStatisticsOrderModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhSilentStatisticsOrderModel) TableName() string {
	return m.table
}
