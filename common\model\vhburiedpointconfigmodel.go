package model

import (
	"context"
	"database/sql"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhBuriedPointConfigModel = (*customVhBuriedPointConfigModel)(nil)

type (
	// VhBuriedPointConfigModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhBuriedPointConfigModel.
	VhBuriedPointConfigModel interface {
		vhBuriedPointConfigModel
		GetTable() string
		Inserts(ctx context.Context, datas []*VhReportDefault) (sql.Result, error)
		FindRows(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}) error
		CountBuilder(field string) squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}, page, pageSize int64) error
	}

	customVhBuriedPointConfigModel struct {
		*defaultVhBuriedPointConfigModel
	}
)

// NewVhBuriedPointConfigModel returns a model for the database table.
func NewVhBuriedPointConfigModel(conn sqlx.SqlConn) VhBuriedPointConfigModel {
	return &customVhBuriedPointConfigModel{
		defaultVhBuriedPointConfigModel: newVhBuriedPointConfigModel(conn),
	}
}

func (m *customVhBuriedPointConfigModel) Inserts(ctx context.Context, datas []*VhReportDefault) (sql.Result, error) {
	if len(datas) == 0 {
		return nil, nil
	}

	sq := squirrel.Insert(m.table).Columns(vhReportDefaultRowsExpectAutoSet)
	for _, data := range datas {
		sq = sq.Values(data.Uid, data.IsLogin, data.Genre, data.Channel, data.RegionId, data.ButtonId, data.Client, data.Mid, data.CreatedTime)
	}

	query, values, err := sq.ToSql()
	if err != nil {
		return nil, err
	}

	ret, err := m.conn.ExecCtx(ctx, query, values...)
	if err != nil {
		return nil, err
	}

	return ret, err
}

func (m *customVhBuriedPointConfigModel) GetTable() string {
	return m.table
}
func (m *customVhBuriedPointConfigModel) FindRows(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := rowBuilder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *customVhBuriedPointConfigModel) CountBuilder(field string) squirrel.SelectBuilder {
	return squirrel.Select("COUNT(" + field + ") as count").From(m.table)
}

func (m *customVhBuriedPointConfigModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64
	err = m.conn.QueryRowCtx(ctx, &count, query, values...)
	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *customVhBuriedPointConfigModel) FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}, page, pageSize int64) error {
	offset := (page - 1) * pageSize

	query, values, err := rowBuilder.Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return nil
	default:
		return err
	}
}
