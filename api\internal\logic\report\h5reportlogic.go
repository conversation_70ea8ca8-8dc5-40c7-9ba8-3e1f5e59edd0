package report

import (
	"context"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type H5ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewH5ReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *H5ReportLogic {
	return &H5ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *H5ReportLogic) H5Report(req *types.H5ReportReq) error {
	l.Lo<PERSON>.<PERSON>r(fmt.Sprintf("H5Report url:%s info:%s", req.Url, req.ErrorInfo))
	return nil
}
