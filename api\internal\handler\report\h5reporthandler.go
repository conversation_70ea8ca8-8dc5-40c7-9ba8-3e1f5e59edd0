package report

import (
	"engine/api/internal/logic/report"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func H5ReportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.H5ReportReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := report.NewH5ReportLogic(r.Context(), svcCtx)
		err := l.H5Report(&req)
		result.HttpResult(r, w, result.<PERSON>ull<PERSON>son{}, err)
	}
}
