package task

import (
	"context"
	"encoding/json"
	"engine/common"
	victoriaMe "engine/common/victoriaMetrics"
	"engine/common/xredis"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"net/http"
	"sort"
	"strconv"
	"sync"
	"time"

	red "github.com/gomodule/redigo/redis"
	"github.com/olivere/elastic/v7"
)

type RankedProduct struct {
	BaseTask
	Metrics  *victoriaMe.MetricsClient
	EsClient *elastic.Client
	Redis    *red.Pool
}

type ProductRanker struct {
	ID            int64     `json:"id"`
	Score         float64   `json:"score"`          //最终得分
	Exposure      float64   `json:"exposure"`       //曝光量
	ExposureScore float64   `json:"exposure_score"` //曝光量得分
	Click         float64   `json:"click"`          //浏览量
	ClickScore    float64   `json:"click_score"`    //浏览量得分
	Order         float64   `json:"order"`          //订单量
	OrderScore    float64   `json:"order_score"`    //订单量得分
	Comment       float64   `json:"comment"`        //评论量
	CommentScore  float64   `json:"comment_score"`  //评论量得分
	BaseScore     float64   `json:"base_score"`     //基础得分(曝光量得分+浏览量得分+订单量得分+评论量得分)
	Manual        int       `json:"manual"`         //手动排序位置
	ManualScore   float64   `json:"manual_score"`   //手动排序得分
	DecayFactor   float64   `json:"decay_factor"`   //时间衰减系数
	IsNew         bool      `json:"is_new"`         //是否新品
	NewScore      float64   `json:"new_score"`      //新品得分（平均分*时间衰减系数）
	AvgScore      float64   `json:"avg_score"`      //平均分（除开新品）
	SalesTime     time.Time `json:"-"`              //上架时间
}

type Cf struct {
	ExposureWeight CfInfo            `json:"exposure_weight"`
	ClickWeight    CfInfo            `json:"click_weight"`
	OrderWeight    CfInfo            `json:"order_weight"`
	CommentWeight  CfInfo            `json:"comment_weight"`
	TimeWeight     []TimeRangeConfig `json:"time_weight"`
}
type CfInfo struct {
	Label string  `json:"label"`
	Value float64 `json:"value"`
}

// TimeRangeConfig 时间范围配置
type TimeRangeConfig struct {
	StartHour float64 `json:"start_hour"` // 起始小时
	EndHour   float64 `json:"end_hour"`   // 结束小时
	StartVal  float64 `json:"start_val"`  // 起始系数
	EndVal    float64 `json:"end_val"`    // 结束系数
}

func (t *RankedProduct) Execute() {
	currTime := time.Now()

	var (
		wait            errgroup.Group
		products        map[int64]*ProductRanker
		metrics         map[int64]map[string]float64
		manualSorting   map[int64]int //商品id -> 排序位置
		cf              Cf
		newProducts     []int64 //新品商品id
		totalBaseScore  float64 // 总分
		oldProductCount int     // 非新品数量
	)

	// 1. 获取商品基本信息和新品（已包含排序和新品标记）
	wait.Go(func() error {
		products = t.getProductBaseInfo()
		return nil
	})

	// 2. 获取指标数据
	wait.Go(func() error {
		metrics = t.queryMetrics()
		return nil
	})

	//3. 获取配置参数
	wait.Go(func() error {
		b, err := red.Bytes(xredis.Do(t.Redis, func(conn red.Conn) (interface{}, error) {
			return conn.Do("GET", "vm:cf")
		}))
		if err != nil && !errors.Is(err, red.ErrNil) {
			return err
		}

		if len(b) > 0 {
			err = json.Unmarshal(b, &cf)
			if err != nil {
				return err
			}
		}
		return nil
	})

	//4. 获取手动设置排序的商品
	wait.Go(func() error {
		manualSorting = t.ManualSorting()
		return nil
	})

	err := wait.Wait()
	if err != nil || len(products) == 0 {
		return
	}

	for pid, product := range products {
		m := map[string]float64{"clicks": 0, "comments": 0, "order_ct": 0, "exposure": 0}
		if info, exists := metrics[pid]; exists {
			m = info
		}

		//曝光量
		product.Exposure = m["exposure"]
		product.ExposureScore = common.TruncationFloat64(m["exposure"] * cf.ExposureWeight.Value)

		// 浏览量
		product.Click = m["clicks"]
		product.ClickScore = common.TruncationFloat64(m["clicks"] * cf.ClickWeight.Value)

		// 评论
		product.Comment = m["comments"]
		product.CommentScore = common.TruncationFloat64(m["comments"] * cf.CommentWeight.Value)

		// 订单
		product.Order = m["order_ct"]
		product.OrderScore = common.TruncationFloat64(m["order_ct"] * cf.OrderWeight.Value)

		product.BaseScore = product.ExposureScore + product.ClickScore +
			product.CommentScore + product.OrderScore

		//新品单独计算
		if product.IsNew {
			newProducts = append(newProducts, pid)
			continue
		}

		// 非新品：累加总分和计数
		totalBaseScore += product.BaseScore
		oldProductCount++

		//最终得分
		product.Score = common.TruncationFloat64(product.BaseScore, 1)
	}

	if len(newProducts) > 0 {
		// 计算平均分（非新品）
		avgScore := common.TruncationFloat64(totalBaseScore / float64(oldProductCount))
		//新品总分计算规则 基础分 + 非新品的平均分 * 时间衰退系数
		for _, pid := range newProducts {
			product := products[pid]
			product.DecayFactor = t.CalculateDecayFactor(product.SalesTime, cf.TimeWeight)
			product.AvgScore = avgScore
			product.NewScore = common.TruncationFloat64(avgScore * product.DecayFactor)
			product.Score = common.TruncationFloat64(product.BaseScore+product.NewScore, 1)
		}
	}

	// 将products转换为切片以便排序
	productSlice := make([]*ProductRanker, 0, len(products))
	for _, product := range products {
		productSlice = append(productSlice, product)
	}

	// 按分数降序排序
	sort.Slice(productSlice, func(i, j int) bool {
		return productSlice[i].Score > productSlice[j].Score
	})

	//手动设置排序
	for pid, sorting := range manualSorting {
		if product, exists := products[pid]; exists {
			// 获取目标位置的索引
			targetIndex := sorting - 1
			if targetIndex < 0 || targetIndex >= len(productSlice) {
				continue
			}

			// 根据目标位置计算新的分值
			var newScore float64
			switch {
			case sorting == 1: // 排第一
				// 取第一名分数加1，确保排在最前
				newScore = productSlice[0].Score + 0.01
			case sorting == len(productSlice): // 排最后
				// 取最后一名分数减1，确保排在最后
				newScore = productSlice[len(productSlice)-1].Score - 0.01
			default: // 排中间
				//因为所有排名的是保留的一位小数，如果要排到后面一位就直接+0.01
				newScore = productSlice[targetIndex].Score + 0.01
			}

			// 更新商品分值
			product.Manual = sorting
			product.ManualScore = newScore - product.Score //如果排到下面去了就是负的
			product.Score = newScore
		}
	}

	// 重新排序
	sort.Slice(productSlice, func(i, j int) bool {
		return productSlice[i].Score > productSlice[j].Score
	})

	// 存储到Redis
	_, err = xredis.Do(t.Redis, func(conn red.Conn) (interface{}, error) {
		if err := conn.Send("MULTI"); err != nil {
			return nil, err
		}

		if err := conn.Send("DEL", "vm:product:scores"); err != nil {
			return nil, err
		}

		// 按照新的顺序存储商品
		for _, product := range products {
			if err := conn.Send("ZADD", "vm:product:scores", product.Score, product.ID); err != nil {
				return nil, err
			}
			if err := t.storeProductToRedis(conn, product); err != nil {
				return nil, err
			}

			//fmt.Printf("id:%s score:%.2f clickRate:%.2f conversionRate:%.2f salesTrend:%.2f comment:%.2f timeDecay:%.2f sort:%.2f \n", product.ID, product.Score, product.ClickRate, product.ConversionRate, product.SalesTrend, product.Comment, product.TimeDecay, product.SortScore)
		}

		if err := conn.Send("EXPIRE", "vm:product:scores", 86400); err != nil {
			return nil, err
		}

		return conn.Do("EXEC")
	})
	if err != nil {
		logx.Error("VmProduct Redis 写入失败:", err)
	}

	logx.Info(fmt.Sprintf("vm排序完成，用时:%.2f ", time.Since(currTime).Seconds()))
}

// CalculateDecayFactor 计算时间衰减系数
func (t *RankedProduct) CalculateDecayFactor(publishTime time.Time, config []TimeRangeConfig) float64 {
	hoursDiff := time.Since(publishTime).Hours()

	// 超过最大范围时返回1.0
	if hoursDiff > config[len(config)-1].EndHour {
		return 1.0
	}

	// 遍历所有时间范围
	for _, r := range config {
		if hoursDiff >= r.StartHour && hoursDiff <= r.EndHour {
			// 线性插值计算衰减系数
			ratio := (hoursDiff - r.StartHour) / (r.EndHour - r.StartHour)
			decay := r.StartVal + (r.EndVal-r.StartVal)*ratio
			//最低为0.1
			if decay < 0.1 {
				decay = 0.1
			}
			return common.TruncationFloat64(decay)
		}
	}

	return 1.0 // 默认无衰减
}

func (t *RankedProduct) storeProductToRedis(conn red.Conn, product *ProductRanker) error {
	key := fmt.Sprintf("vm:product:details:%d", product.ID)
	data, err := json.Marshal(product)
	if err != nil {
		return err
	}
	if err := conn.Send("SET", key, data); err != nil {
		return err
	}
	if err := conn.Send("EXPIRE", key, 86400); err != nil {
		return err
	}
	return nil
}

// 获取商品基本信息（含上架时间）
func (t *RankedProduct) getProductBaseInfo() map[int64]*ProductRanker {
	products := make(map[int64]*ProductRanker)

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("onsale_status", 2),
		elastic.NewTermQuery("is_channel", 0),
		elastic.NewTermsQuery("periods_type", 0),
	)

	// 添加排序条件
	result, err := t.EsClient.Search().
		Index("vinehoo.periods").
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).
						Include("onsale_time")).
		Sort("onsale_time", false). // 添加这行，false表示倒序
		Size(5000).
		Do(context.Background())

	if err != nil {
		logx.Error("ES查询失败: ", err)
		return products
	}

	currT := time.Now()

	for _, hit := range result.Hits.Hits {
		var data struct {
			OnsaleTime string `json:"onsale_time"`
		}
		if err := json.Unmarshal(hit.Source, &data); err == nil {
			salesTime, _ := time.ParseInLocation("2006-01-02 15:04:05", data.OnsaleTime, time.Local)
			id := cast.ToInt64(hit.Id)
			products[id] = &ProductRanker{
				ID:        id,
				SalesTime: salesTime,
				IsNew:     currT.Unix()-salesTime.Unix() <= 86400,
			}
		} else {
			return products
		}
	}
	return products
}

// queryMetrics 并发重构
func (t *RankedProduct) queryMetrics() map[int64]map[string]float64 {
	metrics := make(map[int64]map[string]float64)
	var mu sync.Mutex
	g, _ := errgroup.WithContext(context.Background())

	queries := []struct {
		query string
		field string
	}{
		//最近曝光量
		{`sum(sum_over_time(period_exposure[1d])) by (id)`, "exposure"},
		// 最近点击量
		{`sum(sum_over_time(period_clicks[1d])) by (id)`, "clicks"},
		// 最近的订单量
		{`sum(sum_over_time(period_order_ct[1d])) by (id)`, "order_ct"},
		// 最近7天的评论数
		{`sum(sum_over_time(period_comment[7d])) by (id)`, "comments"},
	}

	for _, q := range queries {
		q := q
		g.Go(func() error {
			data := t.querySingleMetric(q.query, q.field)
			if data == nil {
				logx.Errorf("查询指标[%s]失败，已跳过", q.field)
				return nil
			}
			mu.Lock()
			for pid, v := range data {
				if _, exists := metrics[pid]; !exists {
					metrics[pid] = map[string]float64{"exposure": 0, "clicks": 0, "order_ct": 0, "comments": 0}
				}
				metrics[pid][q.field] = v
			}
			mu.Unlock()
			return nil
		})
	}

	_ = g.Wait()
	return metrics
}

// 查询单个指标
func (t *RankedProduct) querySingleMetric(query string, metricName string) map[int64]float64 {
	result, err := t.Metrics.Query(query)
	if err != nil {
		logx.Errorf("[%s]指标查询失败: %v", metricName, err)
		return nil
	}

	data := make(map[int64]float64)
	for _, item := range result.Data.Result {
		pid := item.Metric["id"]
		if pid == "" {
			continue
		}

		if val, err := strconv.ParseFloat(item.Value[1].(string), 64); err == nil {
			data[cast.ToInt64(pid)] = val
		}
	}
	return data
}

// ManualSorting 获取手动排序位置
func (t *RankedProduct) ManualSorting() map[int64]int {
	var result map[int64]int
	resp, err := http.Get("http://tp6-commodities/commodities/v3/evSort/getActiveSortIds")
	if err != nil {
		logx.Error("获取手动排序商品ID失败:", err)
	} else {
		defer resp.Body.Close()
		var respJson struct {
			ErrorCode int         `json:"error_code"`
			ErrorMsg  string      `json:"error_msg"`
			Data      interface{} `json:"data"`
		}
		if err := json.NewDecoder(resp.Body).Decode(&respJson); err != nil {
			logx.Error("解析手动排序商品ID失败:", err)
		} else if respJson.ErrorCode == 0 {
			if dt, ok := respJson.Data.(map[string]interface{}); ok {
				result = make(map[int64]int)
				for s, i := range dt {
					result[cast.ToInt64(s)] = cast.ToInt(i)
				}
			}
		}
	}
	return result
}

func (t *RankedProduct) Stop() {
	fmt.Println("VmProduct任务退出:", time.Now())
}
