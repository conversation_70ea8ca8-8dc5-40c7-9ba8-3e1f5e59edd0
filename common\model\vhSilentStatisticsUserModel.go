package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhSilentStatisticsUserModel = (*customVhSilentStatisticsUserModel)(nil)

type (
	// VhSilentStatisticsUserModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhSilentStatisticsUserModel.
	VhSilentStatisticsUserModel interface {
		vhSilentStatisticsUserModel
	}

	customVhSilentStatisticsUserModel struct {
		*defaultVhSilentStatisticsUserModel
	}
)

// NewVhSilentStatisticsUserModel returns a model for the database table.
func NewVhSilentStatisticsUserModel(conn sqlx.SqlConn) VhSilentStatisticsUserModel {
	return &customVhSilentStatisticsUserModel{
		defaultVhSilentStatisticsUserModel: newVhSilentStatisticsUserModel(conn),
	}
}
