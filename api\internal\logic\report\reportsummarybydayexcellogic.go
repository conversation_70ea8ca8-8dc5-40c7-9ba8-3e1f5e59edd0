package report

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportSummaryByDayExcelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportSummaryByDayExcelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportSummaryByDayExcelLogic {
	return &ReportSummaryByDayExcelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportSummaryByDayExcelLogic) ReportSummaryByDayExcel(req *types.ReportSummaryByDayReq) (resp *types.ReportSummaryByDayResp, err error) {
	where := squirrel.And{squirrel.Eq{"channel": req.ChannelId}}
	d := strings.Split(req.Date, "-")
	if len(d) != 3 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "日期格式有误")
	}
	t, _ := time.ParseInLocation("2006-01-02", req.Date, time.Local)
	startTime := t.Unix()
	//startTime := time.Date(cast.ToInt(d[0]), time.Month(cast.ToInt(d[1])), 8, 0, 0, 0, 0, time.Local).Unix()
	endTime := startTime + 86399
	where = append(where, squirrel.GtOrEq{"created_time": startTime}, squirrel.LtOrEq{"created_time": endTime})
	if req.RegionId != 0 {
		where = append(where, squirrel.Eq{"region_id": req.RegionId})
	}
	if req.ButtonId != 0 {
		where = append(where, squirrel.Eq{"button_id": req.ButtonId})
	}
	builder := squirrel.Select("uid,FROM_UNIXTIME(created_time,'%H') as h").
		From(l.svcCtx.ReportModel.GetTable()).Where(where).OrderBy("created_time asc")

	var result []*model.VhReportSummaryDay
	err = l.svcCtx.ReportModel.FindRows(l.ctx, builder, &result)
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.ReportSummaryByDayResp)
	if len(result) > 0 {
		var nullStruct struct{}
		currentH := int64(0)
		currentUids := map[string]struct{}{}
		for _, data := range result {
			if currentH != data.H {
				if len(currentUids) > 0 {
					add(resp, currentH, false, int64(len(currentUids)))
				}
				currentH = data.H
				for x := range currentUids {
					delete(currentUids, x)
				}
			}
			add(resp, data.H, true, 1)
			currentUids[data.Uid] = nullStruct
		}
		if len(currentUids) > 0 {
			add(resp, currentH, false, int64(len(currentUids)))
		}
	}

	return resp, nil
}
