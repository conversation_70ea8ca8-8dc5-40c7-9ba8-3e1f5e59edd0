package tencentAdvert

import (
	"context"
	"github.com/antihax/optional"
	"github.com/tencentad/marketing-api-go-sdk/pkg/ads"
	"github.com/tencentad/marketing-api-go-sdk/pkg/api"
	"github.com/tencentad/marketing-api-go-sdk/pkg/config"
	"github.com/tencentad/marketing-api-go-sdk/pkg/model"
	"github.com/zeromicro/go-zero/core/service"
)

const (
	MdWebNm     string = "酒云网小程序"
	MdIosNm     string = "V1上报_ios"
	MdAndroidNm string = "V1上报_android"
	MdWeMiniNm  string = "酒云网 与百万发烧友一起淘酒"
	MdOfflineNm string = "V1上报_offline"
)

// Mapping 需要的行为数据源
type Mapping struct {
	MdWeb     *Set
	MdIos     *Set
	MdAndroid *Set
	MdWeMini  *Set
	MdOffline *Set
}

type Set struct {
	Name            string
	UserActionSetId int64
}

type Ads struct {
	Config *Config
	Client *ads.SDKClient
	Sets   *Mapping
}

func (a *Ads) initActionSets() {
	all, err := a.GetUserActionSetsAll()
	if err != nil {
		panic(err)
	}

	sets := map[string]int64{}
	for _, set := range *all.List {
		if _, ok := sets[*set.Name]; !ok {
			sets[*set.Name] = *set.UserActionSetId
		}
	}
	mapping := &Mapping{
		/*MdIos: &Set{
			Name: MdIosNm,
		},
		MdAndroid: &Set{
			Name: MdAndroidNm,
		},*/
		MdWeb: &Set{
			Name: MdWebNm,
		},
		MdWeMini: &Set{
			Name: MdWeMiniNm,
		},
		/*MdOffline: &Set{
			Name: MdOfflineNm,
		},*/
	}

	//ios
	/*if v, ok := sets[mapping.MdIos.Name]; ok {
		mapping.MdIos.UserActionSetId = v
	} else {
		actionSets, err := a.AddUserActionSets(&model.UserActionSetsAddRequest{
			AccountId:   &a.Config.AccountId,
			Type_:       model.AmUserActionSetType_IOS,
			Name:        &mapping.MdIos.Name,
			MobileAppId: &a.Config.ReportV1IosAppId,
		})
		if err != nil {
			panic(err)
		}
		mapping.MdIos.UserActionSetId = *actionSets.UserActionSetId
	}*/

	/*//android
	if v, ok := sets[mapping.MdAndroid.Name]; ok {
		mapping.MdAndroid.UserActionSetId = v
	} else {
		actionSets, err := a.AddUserActionSets(&model.UserActionSetsAddRequest{
			AccountId:   &a.Config.AccountId,
			Type_:       model.AmUserActionSetType_ANDROID,
			Name:        &mapping.MdAndroid.Name,
			MobileAppId: &a.Config.ReportV1AndroidAppId,
		})
		if err != nil {
			panic(err)
		}
		mapping.MdAndroid.UserActionSetId = *actionSets.UserActionSetId
	}*/

	//web
	if v, ok := sets[mapping.MdWeb.Name]; ok {
		mapping.MdWeb.UserActionSetId = v
	} else {
		actionSets, err := a.AddUserActionSets(&model.UserActionSetsAddRequest{
			AccountId: &a.Config.AccountId,
			Type_:     "WEB",
			Name:      &mapping.MdWeb.Name,
		})
		if err != nil {
			panic(err)
		}
		mapping.MdWeb.UserActionSetId = *actionSets.UserActionSetId
	}

	//we mini
	/*if v, ok := sets[mapping.MdWeMini.Name]; ok {
		mapping.MdWeMini.UserActionSetId = v
	} else {
		actionSets, err := a.AddUserActionSets(&model.UserActionSetsAddRequest{
			AccountId: &a.Config.AccountId,
			Type_:     model.AmUserActionSetType_WECHAT_MINI_PROGRAM,
			Name:      &mapping.MdWeMini.Name,
		})
		if err != nil {
			panic(err)
		}
		mapping.MdWeMini.UserActionSetId = *actionSets.UserActionSetId
	}*/

	/*//offline
	if v, ok := sets[mapping.MdOffline.Name]; ok {
		mapping.MdOffline.UserActionSetId = v
	} else {
		actionSets, err := a.AddUserActionSets(&model.UserActionSetsAddRequest{
			AccountId: &a.Config.AccountId,
			Type_:     "OFFLINE",
			Name:      &mapping.MdOffline.Name,
		})
		if err != nil {
			panic(err)
		}
		mapping.MdOffline.UserActionSetId = *actionSets.UserActionSetId
	}*/

	a.Sets = mapping
}

// GetUserActionSetsAll 获取所有行为数据源
func (a *Ads) GetUserActionSetsAll() (*model.UserActionSetsGetResponseData, error) {
	rst, _, err := a.Client.UserActionSets().Get(*a.Client.Ctx, a.Config.AccountId, &api.UserActionSetsGetOpts{
		Fields: optional.NewInterface([]string{"user_action_set_id", "type", "mobile_app_id", "name", "description", "activate_status", "created_time"}),
	})
	return &rst, err
}

// AddUserActionSets 添加行为数据源
func (a *Ads) AddUserActionSets(data *model.UserActionSetsAddRequest) (*model.UserActionSetsAddResponseData, error) {
	rst, _, err := a.Client.UserActionSets().Add(*a.Client.Ctx, *data)
	return &rst, err
}

// AddUserAction 上传用户行为数据
func (a *Ads) AddUserAction(ctx context.Context, data *model.UserActionsAddRequest) error {
	_, _, err := a.Client.UserActions().Add(ctx, *data)
	return err
}

func (a *Ads) GetUserActionSetReports(ctx context.Context, dateRange model.DateRange, field *api.UserActionSetReportsGetOpts) (*model.UserActionSetReportsGetResponseData, error) {
	rst, _, err := a.Client.UserActionSetReports().Get(ctx, a.Config.AccountId, a.Sets.MdWeMini.UserActionSetId, dateRange, "DAILY", field)
	return &rst, err
}

func GetAds(c *Config, mode string) *Ads {
	a := &Ads{
		Config: c,
		Client: ads.Init(&config.SDKConfig{
			//IsDebug:     true,
			AccessToken: c.AccessToken,
		}),
	}

	if mode == service.ProMode {
		a.Client.UseProduction()
	} else {
		a.Client.UseSandbox() //沙河模式
	}

	a.initActionSets()
	return a
}
