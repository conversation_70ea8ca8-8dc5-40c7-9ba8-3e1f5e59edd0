package buriedPointConfig

import (
	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func AddBuriedPointConfigHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddBuriedPointConfigReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := buriedPointConfig.NewAddBuriedPointConfigLogic(r.Context(), svcCtx)
		err := l.AddBuriedPointConfig(&req)
		result.HttpResult(r, w, result.<PERSON><PERSON><PERSON><PERSON>{}, err)
	}
}
