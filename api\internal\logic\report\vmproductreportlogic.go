package report

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	victoriaMe "engine/common/victoriaMetrics"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type VmProductReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVmProductReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VmProductReportLogic {
	return &VmProductReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VmProductReportLogic) VmProductReport(req *types.VmProductReportReq) error {
	//验证设备
	ls := make([]struct {
		ID         int64
		UID        string
		Device     string
		MetricName string
		TimeStamp  int64
	}, 0)

	for _, d := range req.Data {
		if !common.InSlice(d.MetricName, []string{victoriaMe.PeriodExposure, victoriaMe.PeriodOrderCT}) {
			return xerr.NewErrCodeMsg(xerr.RequestParamError, "无效指标名称")
		}
		if d.MetricName == victoriaMe.PeriodExposure {
			if !common.InSlice(d.Device, []string{"h5", "ios", "android", "hm", "miniapp", "pc"}) {
				return xerr.NewErrCodeMsg(xerr.RequestParamError, "无效设备")
			}
		}

		//秒发过滤
		if d.PeriodType == 1 {
			continue
		}

		ls = append(ls, struct {
			ID         int64
			UID        string
			Device     string
			MetricName string
			TimeStamp  int64
		}{ID: d.Period, UID: d.Uid, Device: d.Device, MetricName: d.MetricName, TimeStamp: d.CreatedTime})
	}

	if len(ls) > 0 {
		err := l.svcCtx.Metrics.PeriodReportBatchMetrics(ls)
		if err != nil {
			l.Logger.Error("上报报错:" + err.Error())
			return xerr.NewErrMsg("指标数据上报失败")
		}
	}

	return nil
}
