package auction

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xredis"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/gomodule/redigo/redis"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"net/url"
	"time"
)

type TencentMomentsLogsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTencentMomentsLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TencentMomentsLogsLogic {
	return &TencentMomentsLogsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func save(requestTime string, uid int64, auctionId int64, genre int64, m model.VhAuctionTencentMomentsLogModel) {
	// 解析时间字符串
	t, err := time.ParseInLocation("2006-01-02 15:04:05", requestTime, time.Local)
	if err != nil {
		t = time.Now()
	}
	_, err = m.InsertWithCreateTime(context.Background(), &model.VhAuctionTencentMomentsLog{
		Uid:        uid,
		AuctionId:  auctionId,
		Genre:      genre,
		CreateTime: t,
	})
	if err != nil {
		logx.Error(fmt.Sprintf("TencentMomentsLogsLogic TencentMomentsLogs TencentMomentsLogModel.Insert err:%e", err))
	}
}

func isRegister(uid interface{}, m model.VhAuctionTencentMomentsLogModel) bool {
	count, err := m.FindCount(context.Background(), model.CountBuilder("id", m.TableName()).Where(squirrel.Eq{"genre": 3, "uid": uid}))
	if err != nil {
		return false
	}

	return count > 0
}

func (l *TencentMomentsLogsLogic) TencentMomentsLogs(req *types.GetawayLogReq) error {
	switch req.RouteURL {
	//出价
	case "/auction/v3/bid":
		if req.ErrCode == 0 && isRegister(req.UID, l.svcCtx.TencentMomentsLogModel) {
			query, err := url.ParseQuery(req.RequestParam)
			if err != nil {
				logx.Error(fmt.Sprintf("TencentMomentsLogsLogic TencentMomentsLogs err:%e", err))
			} else {
				save(req.RequestTime, cast.ToInt64(req.UID), cast.ToInt64(query.Get("id")), 2, l.svcCtx.TencentMomentsLogModel)
			}
		}
		break
	//创建保证金订单
	case "/auction-order/v3/earnest/create":
		if req.ErrCode == 0 && isRegister(req.UID, l.svcCtx.TencentMomentsLogModel) {
			var data map[string]interface{}
			err := json.Unmarshal([]byte(req.RequestBody), &data)
			if err != nil {
				logx.Error(fmt.Sprintf("TencentMomentsLogsLogic TencentMomentsLogs json.Unmarshal err:%e", err))
			} else {
				goodsId := data["goods_id"]

				f := func() (count int64, er error) {
					return l.svcCtx.TencentMomentsLogModel.FindCount(context.Background(), model.CountBuilder("id", l.svcCtx.TencentMomentsLogModel.TableName()).Where(squirrel.Eq{"genre": 1, "auction_id": goodsId, "uid": req.UID}))
				}

				count, err := f()
				if err != nil {
					logx.Error(fmt.Sprintf("TencentMomentsLogsLogic TencentMomentsLogs FindCount err:%e", err))
				} else {
					if count == 0 {
						//延迟7分钟后去查询是否参拍
						time.AfterFunc(time.Minute*7, func() {
							//查询redis数据
							v, e := redis.Ints(xredis.Do(l.svcCtx.Redis, func(conn redis.Conn) (interface{}, error) {
								return conn.Do("Lrange", fmt.Sprintf("vinehoo.auction.%s.security", cast.ToString(goodsId)), 0, -1)
							}))

							if e != nil {
								logx.Error(fmt.Sprintf("TencentMomentsLogsLogic TencentMomentsLogs Lrange err:%e", e))
							} else {
								for _, uid := range v {
									if uid == cast.ToInt(req.UID) {
										count, err = f()
										if err != nil {
											logx.Error(fmt.Sprintf("TencentMomentsLogsLogic TencentMomentsLogs FindCount err:%e", err))
										} else {
											if count == 0 {
												save(req.RequestTime, cast.ToInt64(req.UID), cast.ToInt64(goodsId), 1, l.svcCtx.TencentMomentsLogModel)
											}
										}
									}
								}
							}
						})
					}
				}
			}
		}
		break
	}

	return nil
}
