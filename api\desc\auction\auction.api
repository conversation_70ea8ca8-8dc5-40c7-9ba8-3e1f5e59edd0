syntax = "v1"

info(
    title: "拍卖埋点"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    GetawayLogReq {
        ModuleName string `json:"module_name"`
        RequestMethod string `json:"request_method"`
        Host string `json:"host"`
        RouteURL string `json:"route_url"`
        RequestClient string `json:"request_client"`
        RequestClientVersion interface{} `json:"request_client_version"`
        RequestParam string `json:"request_param"`
        RequestBody string `json:"request_body"`
        UserAgent string `json:"user_agent"`
        IP string `json:"ip"`
        DingtalkDeptID string `json:"dingtalk_dept_id"`
        DingtalkUID string `json:"dingtalk_uid"`
        UID string `json:"uid"`
        AccessToken string `json:"access_token"`
        Logon bool `json:"logon"`
        VosName string `json:"vos_name"`
        Referer string `json:"referer"`
        ErrCode int `json:"err_code"`
        ErrMsg string `json:"err_msg"`
        RequestTime string `json:"request_time"`
        ResponseTime string `json:"response_time"`
    }

    RegisterUidByTencentMomentsReq {
        Genre string `json:"genre,default=tencentMoments"`
    }
)

@server(
    middleware: User
    group : auction
    prefix :/maidian/v3/auction
)

service maidian {
    @handler RegisterUidByTencentMoments //将用户设置为腾讯朋友圈广告过来的标识
    post /registerUidByTencentMoments (RegisterUidByTencentMomentsReq)
}

@server(
    group : auction
    prefix :/maidian/v3/auction
)

service maidian {
    @handler TencentMomentsLogs //腾讯朋友圈广告上报
    post /tencentMomentsLogs (GetawayLogReq)
}