package silentStatistics

import (
	"engine/api/internal/logic/silentStatistics"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SilentStatisticsListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := silentStatistics.NewListLogic(r.Context(), svcCtx)
		resp, err := l.List(&req)
		result.HttpResult(r, w, resp, err)
	}
}
