package model

import (
	"context"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhSilentStatisticsOrderModel = (*customVhSilentStatisticsOrderModel)(nil)

type (
	// VhSilentStatisticsOrderModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhSilentStatisticsOrderModel.
	VhSilentStatisticsOrderModel interface {
		vhSilentStatisticsOrderModel
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}, page, pageSize int64) error
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
	}

	customVhSilentStatisticsOrderModel struct {
		*defaultVhSilentStatisticsOrderModel
	}
)

// NewVhSilentStatisticsOrderModel returns a model for the database table.
func NewVhSilentStatisticsOrderModel(conn sqlx.SqlConn) VhSilentStatisticsOrderModel {
	return &customVhSilentStatisticsOrderModel{
		defaultVhSilentStatisticsOrderModel: newVhSilentStatisticsOrderModel(conn),
	}
}

func (m *customVhSilentStatisticsOrderModel) FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}, page, pageSize int64) error {
	offset := (page - 1) * pageSize

	query, values, err := rowBuilder.Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return nil
	default:
		return err
	}
}
func (m *defaultVhSilentStatisticsOrderModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}
	err = m.conn.QueryRowCtx(ctx, resp, query, values...)
	switch err {
	case sqlc.ErrNotFound:
		return ErrNotFound
	default:
		return err
	}
}
