package huawei

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

//可以放到nacos
const (
	TokenUrl     = "https://connect-api.cloud.huawei.com/api/oauth2/v1/token"
	ClientId     = "1279521070403084736"
	ClientSecret = "72A7C897CC9E37458098800E2F75AFC5EB26CE59FC713323981AFFA06965FD32"
	ReportURL    = "https://connect-api.cloud.huawei.com/api/datasource/v1/track/activate"
	AppId        = 10256451
)

type tokenResp struct {
	AccessToken string `json:"access_token,optional"`
}

type reportResp struct {
	Code int64  `json:"code"`
	Msg  string `json:"msg"`
}

func NewReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportLogic {
	return &ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportLogic) Report(req *types.HuaweiReportReq) error {
	builder := l.svcCtx.ClickCallbackModel.RowBuilder()
	builder = builder.Where(squirrel.Eq{"genre": model.GenreHuawei, "os": req.Os})
	var deviceIdType, deviceId, actionParam string
	if req.EventType == "4" {
		actionParam = fmt.Sprintf(`[{"name":"付费金额","value":%f}]`, req.EventWeight)
	}
	if req.Os == 0 {
		if req.Oaid != "" {
			builder = builder.Where(squirrel.Eq{"oaid": req.Oaid})
			deviceIdType = "OAID"
			deviceId = req.Oaid
		} else if req.Imei != "" {
			deviceIdType = "IMEI_MD5"
			deviceId = req.Imei
			builder = builder.Where(squirrel.Eq{"imei": req.Imei})
		} else {
			//都没有直接返回 匹配不到
			return nil
		}
	} else {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "ios暂不支持")
	}
	result, err := l.svcCtx.ClickCallbackModel.FindOneCustom(l.ctx, builder)
	if err != nil && err != model.ErrNotFound {
		logx.Error(fmt.Sprintf("ReportLogic huawei ClickCallbackModel FindOneCustom err %s", err.Error()))
		return xerr.NewErrCode(xerr.DbError)
	}

	if result != nil {
		//拿token
		var token tokenResp
		tokenPost, err := l.svcCtx.RestyHttp.R().SetResult(&token).SetBody(map[string]interface{}{
			"grant_type":    "client_credentials",
			"client_id":     ClientId,
			"client_secret": ClientSecret,
		}).Post(TokenUrl)
		if err != nil {
			logx.Error(fmt.Sprintf("ReportLogic huawei token RestyHttp err %s", err.Error()))
			return xerr.NewErrMsg("huawei token err: " + err.Error())
		} else {
			logx.Info(string(tokenPost.Body()))
		}

		//上报
		var rsp reportResp
		post, err := l.svcCtx.RestyHttp.R().SetHeaders(map[string]string{
			"client_id": ClientId,
		}).SetAuthToken(token.AccessToken).SetResult(&rsp).SetBody(map[string]interface{}{
			"appId":        AppId,
			"deviceIdType": deviceIdType,
			"deviceId":     deviceId,
			"actionTime":   time.Now().UnixMilli(),
			"actionType":   req.EventType,
			"callBack":     result.Callback,
			"actionParam":  actionParam,
		}).Post(ReportURL)
		if err != nil {
			logx.Error(fmt.Sprintf("ReportLogic huawei RestyHttp err %s", err.Error()))
			return xerr.NewErrMsg("huawei report err: " + err.Error())
		} else {
			logx.Info("ReportLogic huawei RestyHttp info %s", string(post.Body()))
		}
	}
	return nil
}
