syntax = "v1"

info(
    title: "腾讯广告上报"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    TencentAdMiniReportReq struct {
        CreatedTime int64 `json:"created_time" validate:"min=1667974216,timeLt" v:"收集时间戳"`
        OpenId string `json:"openid" validate:"required" v:"用户openid"`
        ActionType string `json:"action_type" validate:"required" v:"用户行为"`
        ClickId string `json:"click_id" validate:"required" v:"clickId"`
        Source int64 `json:"source,default=1"`
        Order *TencentAdMiniReportOrder `json:"order,optional" validate:"required_if=ActionType COMPLETE_ORDER" v:"订单信息"`
    }
    TencentAdMiniReportOrder struct {
        Quantity int64 `json:"quantity" validate:"min=1" v:"单量"`
        Value int64 `json:"value" validate:"min=1" v:"金额"`
    }
)

@server(
    //middleware: User
    group : tencentAd
    prefix :/maidian/v3/tencentAd
)

service maidian {
    @handler MiniReport //小程序上报
    post /miniReport (TencentAdMiniReportReq)
}