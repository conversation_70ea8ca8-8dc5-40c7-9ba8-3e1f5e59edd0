package auction

import (
	"engine/api/internal/logic/auction"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func RegisterUidByTencentMomentsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RegisterUidByTencentMomentsReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := auction.NewRegisterUidByTencentMomentsLogic(r.Context(), svcCtx)
		err := l.RegisterUidByTencentMoments(&req)
		result.HttpResult(r, w, result.<PERSON><PERSON><PERSON><PERSON>{}, err)
	}
}
