// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhSilentStatisticsUserFieldNames          = builder.RawFieldNames(&VhSilentStatisticsUser{})
	vhSilentStatisticsUserRows                = strings.Join(vhSilentStatisticsUserFieldNames, ",")
	vhSilentStatisticsUserRowsExpectAutoSet   = strings.Join(stringx.Remove(vhSilentStatisticsUserFieldNames, "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhSilentStatisticsUserRowsWithPlaceHolder = strings.Join(stringx.Remove(vhSilentStatisticsUserFieldNames, "`uid`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhSilentStatisticsUserModel interface {
		Insert(ctx context.Context, data *VhSilentStatisticsUser) (sql.Result, error)
		FindOne(ctx context.Context, uid int64) (*VhSilentStatisticsUser, error)
		Update(ctx context.Context, data *VhSilentStatisticsUser) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, uid int64) error
	}

	defaultVhSilentStatisticsUserModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhSilentStatisticsUser struct {
		Uid           int64  `db:"uid"`             // 用户id
		Name          string `db:"name"`            // 昵称
		Phone         string `db:"phone"`           // 手机号
		WeUid         string `db:"we_uid"`          // 微信号
		IsAddCs       int64  `db:"is_add_cs"`       // 是否添加客服(1是)
		WakeTime      int64  `db:"wake_time"`       // 唤醒时间
		CouponTime    int64  `db:"coupon_time"`     // 领劵时间
		UseCouponTime int64  `db:"use_coupon_time"` // 使用优惠劵时间
		LastBuyTime   int64  `db:"last_buy_time"`   // 最后下单时间
		BuyCt         int64  `db:"buy_ct"`          // 下单次数(最多记录三次)
	}
)

func newVhSilentStatisticsUserModel(conn sqlx.SqlConn) *defaultVhSilentStatisticsUserModel {
	return &defaultVhSilentStatisticsUserModel{
		conn:  conn,
		table: "`vh_silent_statistics_user`",
	}
}

func (m *defaultVhSilentStatisticsUserModel) Delete(ctx context.Context, uid int64) error {
	query := fmt.Sprintf("delete from %s where `uid` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, uid)
	return err
}

func (m *defaultVhSilentStatisticsUserModel) FindOne(ctx context.Context, uid int64) (*VhSilentStatisticsUser, error) {
	query := fmt.Sprintf("select %s from %s where `uid` = ? limit 1", vhSilentStatisticsUserRows, m.table)
	var resp VhSilentStatisticsUser
	err := m.conn.QueryRowCtx(ctx, &resp, query, uid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhSilentStatisticsUserModel) Insert(ctx context.Context, data *VhSilentStatisticsUser) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhSilentStatisticsUserRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.Name, data.Phone, data.WeUid, data.IsAddCs, data.WakeTime, data.CouponTime, data.UseCouponTime, data.LastBuyTime, data.BuyCt)
	return ret, err
}

func (m *defaultVhSilentStatisticsUserModel) Update(ctx context.Context, data *VhSilentStatisticsUser) error {
	query := fmt.Sprintf("update %s set %s where `uid` = ?", m.table, vhSilentStatisticsUserRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Name, data.Phone, data.WeUid, data.IsAddCs, data.WakeTime, data.CouponTime, data.UseCouponTime, data.LastBuyTime, data.BuyCt, data.Uid)
	return err
}

func (m *defaultVhSilentStatisticsUserModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhSilentStatisticsUserRows).From(m.table)
}

func (m *defaultVhSilentStatisticsUserModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhSilentStatisticsUserModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhSilentStatisticsUserModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhSilentStatisticsUserModel) TableName() string {
	return m.table
}
