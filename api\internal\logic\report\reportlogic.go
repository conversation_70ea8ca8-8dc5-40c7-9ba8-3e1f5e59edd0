package report

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportLogic {
	return &ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportLogic) Report(req *types.ReportDefaultReq) error {
	data := make([]*model.VhReportDefault, 0)

	for _, d := range req.Data {
		//不可能有0的button  只要是0就不存下来
		if d.ButtonId == 0 {
			continue
		}
		//修正ios传错频道
		if d.RegionId == 202000 {
			d.Channel = 3
		}

		data = append(data, &model.VhReportDefault{
			Uid:         d.Uid,
			IsLogin:     d.<PERSON>,
			Genre:       d.<PERSON>re,
			Channel:     d.Channel,
			RegionId:    d.<PERSON>d,
			ButtonId:    d.ButtonId,
			Client:      d.Client,
			Mid:         d.Mid,
			CreatedTime: d.CreatedTime,
		})
	}

	if len(data) > 0 {
		_, err := l.svcCtx.ReportModel.Inserts(l.ctx, data)
		if err != nil {
			logx.Error(fmt.Sprintf("ReportLogic Inserts err %s", err.Error()))
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}
