package report

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type WasmReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWasmReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WasmReportLogic {
	return &WasmReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WasmReportLogic) WasmReport(req *types.WasmReportReq) error {
	_, _ = l.svcCtx.WasmModel.Insert(l.ctx, &model.VhWasmLog{
		UserAgent: req.UserAgent,
	})
	return nil
}
