syntax = "v1"

info(
    title: "百度广告上报"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    BaiduCallbackReq struct {
        ImeiMd5 string `form:"imei_md5,optional"`
        Idfa string `form:"idfa,optional"`
        Oaid string `form:"oaid,optional"`
        Os int64 `form:"os,optional"`
        CallbackUrl string `form:"callback_url,optional"`
        Akey string `form:"akey"`//密钥
    }

    BaiduReportReq struct {
        Oaid string `json:"oaid,optional"`  //安卓oaid
        Imei string `json:"imei,optional"`  //安卓imei
        Idfa string `json:"idfa,optional"`  //苹果idfa
        Os int64 `json:"os,range=[0:1]"`    //0安卓1苹果3其他
        EventType string `json:"event_type"`
        EventWeight float64 `json:"event_weight,default=0"`
    }
)

@server(
    //middleware: User
    group : baidu
    prefix :/maidian/v3/baidu
)


service maidian {
    @handler Callback //点击监测
    get /callback (BaiduCallbackReq)

    @handler Report //上报
    post /report (BaiduReportReq)
}