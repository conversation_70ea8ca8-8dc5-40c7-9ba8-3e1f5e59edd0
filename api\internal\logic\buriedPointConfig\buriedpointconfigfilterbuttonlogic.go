package buriedPointConfig

import (
	"context"
	"encoding/json"
	"strconv"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/Masterminds/squirrel"
	"github.com/olivere/elastic/v7"
	"github.com/zeromicro/go-zero/core/logx"
)

type BuriedPointConfigFilterButtonLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBuriedPointConfigFilterButtonLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BuriedPointConfigFilterButtonLogic {
	return &BuriedPointConfigFilterButtonLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type VinehooButton struct {
	Id    int64  `json:"id"`
	Title string `json:"title"`
}

func (l *BuriedPointConfigFilterButtonLogic) BuriedPointConfigFilterButton(req *types.BuriedPointConfigFilterButtonReq) (resp *types.BuriedPointConfigFilterButtonResp, err error) {
	result := types.BuriedPointConfigFilterButtonResp{
		Button: make([]types.BuriedPointConfigFilterRespButton, 0),
	}
	resp = &result

	info, xerr := l.svcCtx.BuriedPointConfigModel.FindOneRegionsId(l.ctx, req.RegionsId)
	if xerr != nil {
		return
	}
	if info.Id == 0 {
		return
	}

	where := squirrel.Eq{"region_id": info.RegionId, "channel": info.Channel}
	builder := squirrel.Select("DISTINCT(button_id)").From(l.svcCtx.ReportModel.GetTable()).
		Where(where)
	button_ids := make([]int64, 0)
	l.svcCtx.ReportModel.FindRows(context.Background(), builder, &button_ids)

	name_map := make(map[int64]string)
	switch info.Genre {
	case 1: //广告
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,title").From("vh_ad").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 2: //商品
		// 将 []int64 转换为 []string
		ids := make([]string, len(button_ids))
		for i, id := range button_ids {
			ids[i] = strconv.FormatInt(id, 10) // 转换为字符串
		}
		searchResult, _ := l.svcCtx.EsClient.Search().
			Index("vinehoo.periods").
			Query(elastic.NewIdsQuery().Ids(ids...)).
			FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id", "title")).
			Do(context.Background())

		// fmt.Printf("Found %d documents\n", searchResult.TotalHits())
		for _, hit := range searchResult.Hits.Hits {
			var s_info VinehooButton
			json.Unmarshal(hit.Source, &s_info)
			name_map[s_info.Id] = s_info.Title
		}
	case 3: //其他
		for _, v := range button_ids {
			name_map[v] = info.RegionName
		}

	case 4: //金刚区标签
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,label_name as title").From("vh_label").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 5: //专题活动
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,activity_name as title").From("vh_special_activity").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 6: //卡片
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,card_name as title").From("vh_card").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 7: //TAB
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,name as title").From("vh_flash_sale_class").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 8: //秒发分类
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,second_name as title").From("vh_second").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 9: //栏目
		var list []VinehooButton
		where := squirrel.Eq{"id": button_ids}
		builder := squirrel.Select("id,name as title").From("vh_column").Where(where)
		l.svcCtx.CouponIssueModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 10: //福利活动
		var list []VinehooButton
		where := squirrel.Eq{"code": button_ids, "genre": 0}
		builder := squirrel.Select("code as id,name as title").From("vh_buried_point_classify").Where(where)
		l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 11: //优惠区
		var list []VinehooButton
		where := squirrel.Eq{"code": button_ids, "genre": 1}
		builder := squirrel.Select("code as id,name as title").From("vh_buried_point_classify").Where(where)
		l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}

	case 12: //入口统计
		var list []VinehooButton
		where := squirrel.Eq{"code": button_ids, "genre": 2}
		builder := squirrel.Select("code as id,name as title").From("vh_buried_point_classify").Where(where)
		l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}
	case 13: //首页TAB
		var list []VinehooButton
		where := squirrel.Eq{"code": button_ids, "genre": 3}
		builder := squirrel.Select("code as id,name as title").From("vh_buried_point_classify").Where(where)
		l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
		for _, v := range list {
			name_map[v.Id] = v.Title
		}
	}

	//组装名称
	for _, v := range button_ids {
		var name_str string
		if val, ok := name_map[v]; ok {
			name_str = val
		}
		result.Button = append(result.Button, types.BuriedPointConfigFilterRespButton{
			ButtonId:   v,
			ButtonName: name_str,
		})
	}
	return
}
