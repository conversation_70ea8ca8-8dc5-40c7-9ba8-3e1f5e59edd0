package buriedPointConfig

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type BuriedPointConfigChannelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBuriedPointConfigChannelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BuriedPointConfigChannelLogic {
	return &BuriedPointConfigChannelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BuriedPointConfigChannelLogic) BuriedPointConfigChannel() (resp *types.BuriedPointConfigChannelResp, err error) {
	var list []VinehooButton
	result := types.BuriedPointConfigChannelResp{
		Channels: make([]types.BuriedPointConfigChannelRespChannel, 0),
	}
	resp = &result
	builder := squirrel.Select("id,name as title").From("vh_buried_point_channel")
	l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
	for _, v := range list {
		result.Channels = append(result.Channels, types.BuriedPointConfigChannelRespChannel{
			ChannelId:   v.Id,
			ChannelName: v.Title,
		})
	}

	return
}
