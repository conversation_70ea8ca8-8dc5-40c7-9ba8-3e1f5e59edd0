package buriedPointConfig

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type EditBuriedPointConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEditBuriedPointConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EditBuriedPointConfigLogic {
	return &EditBuriedPointConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EditBuriedPointConfigLogic) EditBuriedPointConfig(req *types.EditBuriedPointConfigReq) error {
	builder := squirrel.Select("count(1)").
		From(l.svcCtx.BuriedPointConfigModel.GetTable()).
		Where(squirrel.And{
			squirrel.NotEq{"id": req.Id}, // 确保 id 不等于 req.Id
			squirrel.Eq{"region_id": req.RegionId},
		})

	count, err := l.svcCtx.BuriedPointConfigModel.FindCount(context.Background(), builder)
	if err != nil {
		return xerr.NewErrMsg("添加埋点配置失败")
	}
	if count > 0 {
		return xerr.NewErrMsg("该区域已存在埋点配置")
	}
	info, _ := l.svcCtx.BuriedPointConfigModel.FindOne(context.Background(), req.Id)
	if info.Id == 0 {
		return xerr.NewErrMsg("该埋点配置不存在")
	}

	err = l.svcCtx.BuriedPointConfigModel.Update(context.Background(), &model.VhBuriedPointConfig{
		Id:          req.Id,
		Genre:       req.Genre,
		Channel:     req.Channel,
		RegionId:    req.RegionId,
		RegionName:  req.RegionName,
		CreatedTime: info.CreatedTime,
	})
	if err != nil {
		return xerr.NewErrMsg("修改埋点配置失败")
	}

	return nil
}
