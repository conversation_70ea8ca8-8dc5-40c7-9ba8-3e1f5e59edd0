syntax = "v1"

info(
    title: "华为广告上报"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    HuaweiCallbackReq struct {
        Callback string `form:"callback"`
        Oaid string `form:"oaid,optional"`           //安卓oaid IdType不为0时
        Imei string `form:"imei,optional"`           //安卓imei IdType为0时
    }

    HuaweiReportReq struct {
        Oaid string `json:"oaid,optional"`  //安卓oaid
        Imei string `json:"imei,optional"`  //安卓imei
        Os int64 `json:"os,range=[0:1]"`    //0安卓1苹果3其他
        EventType string `json:"event_type"`
        EventWeight float64 `json:"event_weight,default=0"`
    }
)

@server(
    //middleware: User
    group : huawei
    prefix :/maidian/v3/huawei
)


service maidian {
    @handler Callback //点击监测
    get /callback (HuaweiCallbackReq)

    @handler Report //上报
    post /report (HuaweiReportReq)
}